<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工单配置 - 项目管理平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        .search-bar {
            display: flex;
            align-items: center;
            background-color: var(--light-gray);
            border-radius: 10px;
            padding: 8px 12px;
            margin-bottom: 16px;
        }

        .search-bar input {
            flex: 1;
            border: none;
            background: transparent;
            margin-left: 8px;
            font-size: 15px;
        }

        .search-bar input:focus {
            outline: none;
        }

        .filter-tabs {
            display: flex;
            overflow-x: auto;
            margin-bottom: 16px;
            -webkit-overflow-scrolling: touch;
        }

        .filter-tab {
            padding: 8px 16px;
            border-radius: 100px;
            font-size: 14px;
            white-space: nowrap;
            margin-right: 8px;
        }

        .filter-tab.active {
            background-color: var(--primary-color);
            color: white;
        }

        .filter-tab:not(.active) {
            background-color: var(--light-gray);
            color: var(--dark-gray);
        }

        .workorder-card {
            background-color: white;
            border-radius: 12px;
            margin-bottom: 16px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            overflow: hidden;
        }

        .workorder-header {
            padding: 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid var(--light-gray);
        }

        .workorder-title {
            display: flex;
            align-items: center;
        }

        .workorder-icon {
            width: 36px;
            height: 36px;
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 16px;
            color: white;
        }

        .workorder-icon.repair {
            background-color: var(--primary-color);
        }

        .workorder-icon.maintenance {
            background-color: var(--warning-color);
        }

        .workorder-icon.installation {
            background-color: var(--success-color);
        }

        .workorder-name {
            font-size: 17px;
            font-weight: 600;
        }

        .workorder-id {
            font-size: 14px;
            color: var(--dark-gray);
        }

        .workorder-status {
            font-size: 13px;
            padding: 4px 10px;
            border-radius: 100px;
        }

        .workorder-status.pending {
            background-color: rgba(255, 149, 0, 0.1);
            color: var(--warning-color);
        }

        .workorder-status.processing {
            background-color: rgba(0, 122, 255, 0.1);
            color: var(--primary-color);
        }

        .workorder-status.completed {
            background-color: rgba(52, 199, 89, 0.1);
            color: var(--success-color);
        }

        .workorder-content {
            padding: 16px;
        }

        .workorder-description {
            font-size: 15px;
            color: var(--dark-gray);
            margin-bottom: 12px;
        }

        .workorder-details {
            background-color: var(--light-gray);
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 16px;
        }

        .workorder-detail-item {
            display: flex;
            margin-bottom: 8px;
        }

        .workorder-detail-item:last-child {
            margin-bottom: 0;
        }

        .detail-label {
            width: 80px;
            font-size: 14px;
            color: var(--dark-gray);
        }

        .detail-value {
            flex: 1;
            font-size: 14px;
        }

        .workorder-actions {
            display: flex;
            gap: 8px;
        }

        .workorder-action-btn {
            flex: 1;
            padding: 10px 0;
            border-radius: 10px;
            font-size: 14px;
            font-weight: 500;
            text-align: center;
            background-color: var(--light-gray);
            color: var(--dark-gray);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
            transition: all var(--transition-fast);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .workorder-action-btn i {
            margin-right: 6px;
        }

        .workorder-action-btn:active {
            transform: scale(0.98);
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
        }

        .workorder-action-btn.primary {
            background-color: rgba(0, 122, 255, 0.1);
            color: var(--primary-color);
            box-shadow: 0 1px 3px rgba(0, 122, 255, 0.1);
        }

        .workorder-action-btn.primary:active {
            background-color: rgba(0, 122, 255, 0.15);
            box-shadow: 0 1px 2px rgba(0, 122, 255, 0.05);
        }

        /* 工单来源标签 */
        .source-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 4px;
            font-size: 12px;
            margin-left: 8px;
        }

        .source-badge.user {
            background-color: rgba(0, 122, 255, 0.1);
            color: var(--primary-color);
        }

        .source-badge.alert {
            background-color: rgba(255, 45, 85, 0.1);
            color: var(--danger-color);
        }

        /* 标签内容区域 */
        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* 浮动添加按钮样式 */
        .add-button {
            position: fixed;
            bottom: 100px;
            right: 20px;
            width: 56px;
            height: 56px;
            border-radius: 50%;
            background-color: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
            z-index: 50;
            transition: all var(--transition-fast);
        }

        .add-button:active {
            transform: scale(0.95);
            box-shadow: 0 2px 8px rgba(0, 122, 255, 0.2);
        }

        /* 新建工单表单样式 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: white;
            z-index: 100;
            overflow-y: auto;
        }

        .modal.show {
            display: block;
        }

        .form-container {
            padding: 16px;
            animation: fadeIn 0.3s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .form-section {
            margin-bottom: 24px;
        }

        .form-section-title {
            font-size: 17px;
            font-weight: 600;
            margin-bottom: 16px;
            color: var(--text-color);
            display: flex;
            align-items: center;
        }

        .form-section-title i {
            margin-right: 8px;
            color: var(--primary-color);
            font-size: 18px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            font-size: 15px;
            font-weight: 500;
        }

        .required-mark {
            color: var(--danger-color);
            margin-left: 4px;
        }

        .form-control {
            width: 100%;
            padding: 12px 16px;
            border-radius: 12px;
            border: 1px solid var(--medium-gray);
            font-size: 16px;
            background-color: var(--background-color);
            transition: all var(--transition-fast);
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
        }

        .form-select {
            appearance: none;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%238e8e93'%3E%3Cpath d='M7 10l5 5 5-5z'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 12px center;
            background-size: 20px;
            padding-right: 40px;
            width: 100%;
            padding: 12px 16px;
            border-radius: 12px;
            border: 1px solid var(--medium-gray);
            font-size: 16px;
            background-color: var(--background-color);
            transition: all var(--transition-fast);
        }

        .form-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
        }

        .form-textarea {
            min-height: 120px;
            resize: vertical;
            width: 100%;
            padding: 12px 16px;
            border-radius: 12px;
            border: 1px solid var(--medium-gray);
            font-size: 16px;
            background-color: var(--background-color);
            transition: all var(--transition-fast);
        }

        .form-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
        }

        .image-upload {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
        }

        .upload-box {
            width: 80px;
            height: 80px;
            border: 1px dashed var(--medium-gray);
            border-radius: 12px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: var(--dark-gray);
            font-size: 12px;
            cursor: pointer;
            transition: all var(--transition-fast);
        }

        .upload-box:active {
            background-color: rgba(0, 0, 0, 0.05);
            transform: scale(0.98);
        }

        .upload-box i {
            font-size: 20px;
            margin-bottom: 4px;
            color: var(--primary-color);
        }

        .image-preview {
            width: 80px;
            height: 80px;
            border-radius: 12px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .image-preview img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .image-preview .remove-image {
            position: absolute;
            top: 4px;
            right: 4px;
            width: 22px;
            height: 22px;
            border-radius: 50%;
            background-color: rgba(0, 0, 0, 0.6);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
            transition: all var(--transition-fast);
        }

        .image-preview .remove-image:active {
            transform: scale(0.9);
            background-color: rgba(0, 0, 0, 0.8);
        }

        .form-hint {
            font-size: 13px;
            color: var(--dark-gray);
            margin-top: 6px;
        }

        .form-actions {
            display: flex;
            gap: 12px;
            margin-top: 32px;
        }

        .btn-block {
            flex: 1;
        }

        .btn {
            padding: 12px 16px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 500;
            text-align: center;
            transition: all var(--transition-fast);
        }

        .btn:active {
            transform: scale(0.98);
        }

        .btn-secondary {
            background-color: var(--light-gray);
            color: var(--dark-gray);
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
            box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
        }

        .btn-primary:active {
            box-shadow: 0 1px 4px rgba(0, 122, 255, 0.2);
        }

        /* 工单图片样式 */
        .workorder-images {
            display: flex;
            gap: 8px;
            margin-top: 12px;
            overflow-x: auto;
            padding-bottom: 4px;
        }

        .workorder-image {
            width: 80px;
            height: 80px;
            border-radius: 8px;
            overflow: hidden;
            flex-shrink: 0;
        }

        .workorder-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
    </style>
</head>
<body>
    <!-- Status Bar -->
    <div class="status-bar">
        <div>9:41</div>
        <div class="status-bar-icons">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>

    <!-- Header -->
    <div class="header">
        <div class="header-left">
            <a href="modules.html" class="text-gray-600">
                <i class="fas fa-arrow-left"></i>
            </a>
        </div>
        <div class="header-title">工单配置</div>
        <div class="header-right">
            <i class="fas fa-search text-gray-600"></i>
        </div>
    </div>

    <!-- Content -->
    <div class="content">
        <!-- Search Bar -->
        <div class="search-bar">
            <i class="fas fa-search text-gray-400"></i>
            <input type="text" placeholder="搜索工单...">
        </div>

        <!-- Filter Tabs -->
        <div class="filter-tabs">
            <div class="filter-tab active" onclick="switchTab('all')">全部</div>
            <div class="filter-tab" onclick="switchTab('pending')">待处理</div>
            <div class="filter-tab" onclick="switchTab('processing')">处理中</div>
            <div class="filter-tab" onclick="switchTab('completed')">已完成</div>
            <div class="filter-tab">维修</div>
            <div class="filter-tab">维护</div>
            <div class="filter-tab">安装</div>
        </div>

        <!-- 全部工单标签内容 -->
        <div id="all-tab" class="tab-content active">
        <div class="workorder-card">
            <div class="workorder-header">
                <div class="workorder-title">
                    <div class="workorder-icon repair">
                        <i class="fas fa-tools"></i>
                    </div>
                    <div>
                        <div class="workorder-name">网关设备维修</div>
                        <div class="workorder-id">
                            工单号: WO-2024050601
                            <span class="source-badge alert">报警生成</span>
                        </div>
                    </div>
                </div>
                <div class="workorder-status pending">待处理</div>
            </div>
            <div class="workorder-content">
                <div class="workorder-description">
                    西区网关设备离线，需要现场检查并修复
                </div>
                <div class="workorder-details">
                    <div class="workorder-detail-item">
                        <div class="detail-label">设备:</div>
                        <div class="detail-value">网关 GW-003</div>
                    </div>
                    <div class="workorder-detail-item">
                        <div class="detail-label">位置:</div>
                        <div class="detail-value">西区网关机房</div>
                    </div>
                    <div class="workorder-detail-item">
                        <div class="detail-label">创建时间:</div>
                        <div class="detail-value">2024-05-06 09:15</div>
                    </div>
                    <div class="workorder-detail-item">
                        <div class="detail-label">优先级:</div>
                        <div class="detail-value text-red-500">高</div>
                    </div>
                </div>
                <div class="workorder-images">
                    <div class="workorder-image">
                        <img src="https://via.placeholder.com/80x80/e0e0e0/808080?text=图片1" alt="故障图片">
                    </div>
                    <div class="workorder-image">
                        <img src="https://via.placeholder.com/80x80/e0e0e0/808080?text=图片2" alt="故障图片">
                    </div>
                </div>
                <div class="workorder-actions">
                    <div class="workorder-action-btn">
                        <i class="fas fa-user-plus mr-1"></i> 分配
                    </div>
                    <div class="workorder-action-btn primary">
                        <i class="fas fa-play mr-1"></i> 开始处理
                    </div>
                </div>
            </div>
        </div>

        <div class="workorder-card">
            <div class="workorder-header">
                <div class="workorder-title">
                    <div class="workorder-icon maintenance">
                        <i class="fas fa-wrench"></i>
                    </div>
                    <div>
                        <div class="workorder-name">空调系统维护</div>
                        <div class="workorder-id">
                            工单号: WO-2024050502
                            <span class="source-badge user">用户创建</span>
                        </div>
                    </div>
                </div>
                <div class="workorder-status processing">处理中</div>
            </div>
            <div class="workorder-content">
                <div class="workorder-description">
                    数据中心空调系统定期维护，检查制冷效果
                </div>
                <div class="workorder-details">
                    <div class="workorder-detail-item">
                        <div class="detail-label">设备:</div>
                        <div class="detail-value">中央空调系统</div>
                    </div>
                    <div class="workorder-detail-item">
                        <div class="detail-label">位置:</div>
                        <div class="detail-value">E栋数据中心</div>
                    </div>
                    <div class="workorder-detail-item">
                        <div class="detail-label">创建时间:</div>
                        <div class="detail-value">2024-05-05 14:30</div>
                    </div>
                    <div class="workorder-detail-item">
                        <div class="detail-label">处理人:</div>
                        <div class="detail-value">王五</div>
                    </div>
                </div>
                <div class="workorder-images">
                    <div class="workorder-image">
                        <img src="https://via.placeholder.com/80x80/e0e0e0/808080?text=图片1" alt="故障图片">
                    </div>
                    <div class="workorder-image">
                        <img src="https://via.placeholder.com/80x80/e0e0e0/808080?text=图片2" alt="故障图片">
                    </div>
                    <div class="workorder-image">
                        <img src="https://via.placeholder.com/80x80/e0e0e0/808080?text=图片3" alt="故障图片">
                    </div>
                </div>
                <div class="workorder-actions">
                    <div class="workorder-action-btn">
                        <i class="fas fa-comment-alt mr-1"></i> 添加备注
                    </div>
                    <div class="workorder-action-btn primary">
                        <i class="fas fa-check mr-1"></i> 完成工单
                    </div>
                </div>
            </div>
        </div>

        <div class="workorder-card">
            <div class="workorder-header">
                <div class="workorder-title">
                    <div class="workorder-icon installation">
                        <i class="fas fa-plug"></i>
                    </div>
                    <div>
                        <div class="workorder-name">新设备安装</div>
                        <div class="workorder-id">
                            工单号: WO-2024050401
                            <span class="source-badge user">用户创建</span>
                        </div>
                    </div>
                </div>
                <div class="workorder-status completed">已完成</div>
            </div>
            <div class="workorder-content">
                <div class="workorder-description">
                    在A栋办公楼安装新的温湿度传感器
                </div>
                <div class="workorder-details">
                    <div class="workorder-detail-item">
                        <div class="detail-label">设备:</div>
                        <div class="detail-value">温湿度传感器 TS-008</div>
                    </div>
                    <div class="workorder-detail-item">
                        <div class="detail-label">位置:</div>
                        <div class="detail-value">A栋办公楼 3楼</div>
                    </div>
                    <div class="workorder-detail-item">
                        <div class="detail-label">创建时间:</div>
                        <div class="detail-value">2024-05-04 10:00</div>
                    </div>
                    <div class="workorder-detail-item">
                        <div class="detail-label">完成时间:</div>
                        <div class="detail-value">2024-05-04 15:45</div>
                    </div>
                </div>
                <div class="workorder-images">
                    <div class="workorder-image">
                        <img src="https://via.placeholder.com/80x80/e0e0e0/808080?text=图片1" alt="安装图片">
                    </div>
                    <div class="workorder-image">
                        <img src="https://via.placeholder.com/80x80/e0e0e0/808080?text=图片2" alt="安装图片">
                    </div>
                </div>
                <div class="workorder-actions">
                    <div class="workorder-action-btn">
                        <i class="fas fa-file-alt mr-1"></i> 查看报告
                    </div>
                    <div class="workorder-action-btn primary">
                        <i class="fas fa-copy mr-1"></i> 复制工单
                    </div>
                </div>
            </div>
        </div>
        </div>

        <!-- 待处理工单标签内容 -->
        <div id="pending-tab" class="tab-content">
            <div class="workorder-card">
                <div class="workorder-header">
                    <div class="workorder-title">
                        <div class="workorder-icon repair">
                            <i class="fas fa-tools"></i>
                        </div>
                        <div>
                            <div class="workorder-name">网关设备维修</div>
                            <div class="workorder-id">
                                工单号: WO-2024050601
                                <span class="source-badge alert">报警生成</span>
                            </div>
                        </div>
                    </div>
                    <div class="workorder-status pending">待处理</div>
                </div>
                <div class="workorder-content">
                    <div class="workorder-description">
                        西区网关设备离线，需要现场检查并修复
                    </div>
                    <div class="workorder-details">
                        <div class="workorder-detail-item">
                            <div class="detail-label">设备:</div>
                            <div class="detail-value">网关 GW-003</div>
                        </div>
                        <div class="workorder-detail-item">
                            <div class="detail-label">位置:</div>
                            <div class="detail-value">西区网关机房</div>
                        </div>
                        <div class="workorder-detail-item">
                            <div class="detail-label">创建时间:</div>
                            <div class="detail-value">2024-05-06 09:15</div>
                        </div>
                        <div class="workorder-detail-item">
                            <div class="detail-label">优先级:</div>
                            <div class="detail-value text-red-500">高</div>
                        </div>
                    </div>
                    <div class="workorder-images">
                        <div class="workorder-image">
                            <img src="https://via.placeholder.com/80x80/e0e0e0/808080?text=图片1" alt="故障图片">
                        </div>
                        <div class="workorder-image">
                            <img src="https://via.placeholder.com/80x80/e0e0e0/808080?text=图片2" alt="故障图片">
                        </div>
                    </div>
                    <div class="workorder-actions">
                        <div class="workorder-action-btn">
                            <i class="fas fa-user-plus mr-1"></i> 分配
                        </div>
                        <div class="workorder-action-btn primary">
                            <i class="fas fa-play mr-1"></i> 开始处理
                        </div>
                    </div>
                </div>
            </div>

            <div class="workorder-card">
                <div class="workorder-header">
                    <div class="workorder-title">
                        <div class="workorder-icon repair">
                            <i class="fas fa-tools"></i>
                        </div>
                        <div>
                            <div class="workorder-name">摄像头故障</div>
                            <div class="workorder-id">
                                工单号: WO-2024050603
                                <span class="source-badge user">用户创建</span>
                            </div>
                        </div>
                    </div>
                    <div class="workorder-status pending">待处理</div>
                </div>
                <div class="workorder-content">
                    <div class="workorder-description">
                        南区停车场3号摄像头画面模糊，需要调整或更换
                    </div>
                    <div class="workorder-details">
                        <div class="workorder-detail-item">
                            <div class="detail-label">设备:</div>
                            <div class="detail-value">摄像头 CAM-S03</div>
                        </div>
                        <div class="workorder-detail-item">
                            <div class="detail-label">位置:</div>
                            <div class="detail-value">南区停车场</div>
                        </div>
                        <div class="workorder-detail-item">
                            <div class="detail-label">创建时间:</div>
                            <div class="detail-value">2024-05-06 11:30</div>
                        </div>
                        <div class="workorder-detail-item">
                            <div class="detail-label">优先级:</div>
                            <div class="detail-value text-yellow-500">中</div>
                        </div>
                    </div>
                    <div class="workorder-images">
                        <div class="workorder-image">
                            <img src="https://via.placeholder.com/80x80/e0e0e0/808080?text=图片1" alt="故障图片">
                        </div>
                    </div>
                    <div class="workorder-actions">
                        <div class="workorder-action-btn">
                            <i class="fas fa-user-plus mr-1"></i> 分配
                        </div>
                        <div class="workorder-action-btn primary">
                            <i class="fas fa-play mr-1"></i> 开始处理
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 处理中工单标签内容 -->
        <div id="processing-tab" class="tab-content">
            <div class="workorder-card">
                <div class="workorder-header">
                    <div class="workorder-title">
                        <div class="workorder-icon maintenance">
                            <i class="fas fa-wrench"></i>
                        </div>
                        <div>
                            <div class="workorder-name">空调系统维护</div>
                            <div class="workorder-id">
                                工单号: WO-2024050502
                                <span class="source-badge user">用户创建</span>
                            </div>
                        </div>
                    </div>
                    <div class="workorder-status processing">处理中</div>
                </div>
                <div class="workorder-content">
                    <div class="workorder-description">
                        数据中心空调系统定期维护，检查制冷效果
                    </div>
                    <div class="workorder-details">
                        <div class="workorder-detail-item">
                            <div class="detail-label">设备:</div>
                            <div class="detail-value">中央空调系统</div>
                        </div>
                        <div class="workorder-detail-item">
                            <div class="detail-label">位置:</div>
                            <div class="detail-value">E栋数据中心</div>
                        </div>
                        <div class="workorder-detail-item">
                            <div class="detail-label">创建时间:</div>
                            <div class="detail-value">2024-05-05 14:30</div>
                        </div>
                        <div class="workorder-detail-item">
                            <div class="detail-label">处理人:</div>
                            <div class="detail-value">王五</div>
                        </div>
                    </div>
                    <div class="workorder-images">
                        <div class="workorder-image">
                            <img src="https://via.placeholder.com/80x80/e0e0e0/808080?text=图片1" alt="故障图片">
                        </div>
                        <div class="workorder-image">
                            <img src="https://via.placeholder.com/80x80/e0e0e0/808080?text=图片2" alt="故障图片">
                        </div>
                        <div class="workorder-image">
                            <img src="https://via.placeholder.com/80x80/e0e0e0/808080?text=图片3" alt="故障图片">
                        </div>
                    </div>
                    <div class="workorder-actions">
                        <div class="workorder-action-btn">
                            <i class="fas fa-comment-alt mr-1"></i> 添加备注
                        </div>
                        <div class="workorder-action-btn primary">
                            <i class="fas fa-check mr-1"></i> 完成工单
                        </div>
                    </div>
                </div>
            </div>

            <div class="workorder-card">
                <div class="workorder-header">
                    <div class="workorder-title">
                        <div class="workorder-icon repair">
                            <i class="fas fa-tools"></i>
                        </div>
                        <div>
                            <div class="workorder-name">门禁系统故障</div>
                            <div class="workorder-id">
                                工单号: WO-2024050505
                                <span class="source-badge alert">报警生成</span>
                            </div>
                        </div>
                    </div>
                    <div class="workorder-status processing">处理中</div>
                </div>
                <div class="workorder-content">
                    <div class="workorder-description">
                        B栋一楼门禁系统无法正常读卡，需要检修
                    </div>
                    <div class="workorder-details">
                        <div class="workorder-detail-item">
                            <div class="detail-label">设备:</div>
                            <div class="detail-value">门禁 AC-B101</div>
                        </div>
                        <div class="workorder-detail-item">
                            <div class="detail-label">位置:</div>
                            <div class="detail-value">B栋一楼大厅</div>
                        </div>
                        <div class="workorder-detail-item">
                            <div class="detail-label">创建时间:</div>
                            <div class="detail-value">2024-05-05 16:45</div>
                        </div>
                        <div class="workorder-detail-item">
                            <div class="detail-label">处理人:</div>
                            <div class="detail-value">李四</div>
                        </div>
                    </div>
                    <div class="workorder-images">
                        <div class="workorder-image">
                            <img src="https://via.placeholder.com/80x80/e0e0e0/808080?text=图片1" alt="故障图片">
                        </div>
                    </div>
                    <div class="workorder-actions">
                        <div class="workorder-action-btn">
                            <i class="fas fa-comment-alt mr-1"></i> 添加备注
                        </div>
                        <div class="workorder-action-btn primary">
                            <i class="fas fa-check mr-1"></i> 完成工单
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 已完成工单标签内容 -->
        <div id="completed-tab" class="tab-content">
            <div class="workorder-card">
                <div class="workorder-header">
                    <div class="workorder-title">
                        <div class="workorder-icon installation">
                            <i class="fas fa-plug"></i>
                        </div>
                        <div>
                            <div class="workorder-name">新设备安装</div>
                            <div class="workorder-id">
                                工单号: WO-2024050401
                                <span class="source-badge user">用户创建</span>
                            </div>
                        </div>
                    </div>
                    <div class="workorder-status completed">已完成</div>
                </div>
                <div class="workorder-content">
                    <div class="workorder-description">
                        在A栋办公楼安装新的温湿度传感器
                    </div>
                    <div class="workorder-details">
                        <div class="workorder-detail-item">
                            <div class="detail-label">设备:</div>
                            <div class="detail-value">温湿度传感器 TS-008</div>
                        </div>
                        <div class="workorder-detail-item">
                            <div class="detail-label">位置:</div>
                            <div class="detail-value">A栋办公楼 3楼</div>
                        </div>
                        <div class="workorder-detail-item">
                            <div class="detail-label">创建时间:</div>
                            <div class="detail-value">2024-05-04 10:00</div>
                        </div>
                        <div class="workorder-detail-item">
                            <div class="detail-label">完成时间:</div>
                            <div class="detail-value">2024-05-04 15:45</div>
                        </div>
                    </div>
                    <div class="workorder-images">
                        <div class="workorder-image">
                            <img src="https://via.placeholder.com/80x80/e0e0e0/808080?text=图片1" alt="安装图片">
                        </div>
                        <div class="workorder-image">
                            <img src="https://via.placeholder.com/80x80/e0e0e0/808080?text=图片2" alt="安装图片">
                        </div>
                    </div>
                    <div class="workorder-actions">
                        <div class="workorder-action-btn">
                            <i class="fas fa-file-alt mr-1"></i> 查看报告
                        </div>
                        <div class="workorder-action-btn primary">
                            <i class="fas fa-copy mr-1"></i> 复制工单
                        </div>
                    </div>
                </div>
            </div>

            <div class="workorder-card">
                <div class="workorder-header">
                    <div class="workorder-title">
                        <div class="workorder-icon repair">
                            <i class="fas fa-tools"></i>
                        </div>
                        <div>
                            <div class="workorder-name">照明系统修复</div>
                            <div class="workorder-id">
                                工单号: WO-2024050301
                                <span class="source-badge alert">报警生成</span>
                            </div>
                        </div>
                    </div>
                    <div class="workorder-status completed">已完成</div>
                </div>
                <div class="workorder-content">
                    <div class="workorder-description">
                        C栋走廊照明系统故障，部分灯具无法正常工作
                    </div>
                    <div class="workorder-details">
                        <div class="workorder-detail-item">
                            <div class="detail-label">设备:</div>
                            <div class="detail-value">照明系统 C-L2</div>
                        </div>
                        <div class="workorder-detail-item">
                            <div class="detail-label">位置:</div>
                            <div class="detail-value">C栋二楼走廊</div>
                        </div>
                        <div class="workorder-detail-item">
                            <div class="detail-label">创建时间:</div>
                            <div class="detail-value">2024-05-03 09:20</div>
                        </div>
                        <div class="workorder-detail-item">
                            <div class="detail-label">完成时间:</div>
                            <div class="detail-value">2024-05-03 14:10</div>
                        </div>
                    </div>
                    <div class="workorder-images">
                        <div class="workorder-image">
                            <img src="https://via.placeholder.com/80x80/e0e0e0/808080?text=图片1" alt="故障图片">
                        </div>
                        <div class="workorder-image">
                            <img src="https://via.placeholder.com/80x80/e0e0e0/808080?text=图片2" alt="故障图片">
                        </div>
                    </div>
                    <div class="workorder-actions">
                        <div class="workorder-action-btn">
                            <i class="fas fa-file-alt mr-1"></i> 查看报告
                        </div>
                        <div class="workorder-action-btn primary">
                            <i class="fas fa-copy mr-1"></i> 复制工单
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加按钮 -->
    <a href="#" class="add-button animate-pulse" onclick="openModal()">
        <i class="fas fa-plus"></i>
    </a>

    <!-- 新建工单表单 -->
    <div id="createWorkorderModal" class="modal">
        <!-- Header -->
        <div class="header">
            <div class="header-left">
                <a href="#" onclick="closeModal()" class="text-gray-600">
                    <i class="fas fa-arrow-left"></i>
                </a>
            </div>
            <div class="header-title">新建工单</div>
            <div class="header-right">
                <i class="fas fa-check text-primary-500"></i>
            </div>
        </div>

        <!-- Content -->
        <div class="content">
            <div class="form-container animate-fade-in">
                <div class="form-section">
                    <div class="form-section-title">
                        <i class="fas fa-info-circle"></i>
                        <span>基本信息</span>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="workorder-space">
                            所属空间
                        </label>
                        <select id="workorder-space" class="form-control form-select">
                            <option value="">无</option>
                            <option value="1">A栋办公楼</option>
                            <option value="2">B栋办公楼</option>
                            <option value="3">C栋办公楼</option>
                            <option value="4">数据中心</option>
                            <option value="5">停车场</option>
                        </select>
                        <div class="form-hint">选择故障设备所在的空间</div>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="workorder-device">
                            故障设备
                            <span class="required-mark">*</span>
                        </label>
                        <select id="workorder-device" class="form-control form-select" required>
                            <option value="">请选择设备</option>
                            <option value="1">网关设备 GW-001</option>
                            <option value="2">网关设备 GW-002</option>
                            <option value="3">网关设备 GW-003</option>
                            <option value="4">摄像头 CAM-A01</option>
                            <option value="5">摄像头 CAM-B02</option>
                            <option value="6">门禁系统 AC-A101</option>
                            <option value="7">门禁系统 AC-B101</option>
                            <option value="8">空调系统</option>
                            <option value="9">照明系统</option>
                        </select>
                        <div class="form-hint">选择需要维修的设备</div>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="workorder-location">
                            故障点位
                        </label>
                        <select id="workorder-location" class="form-control form-select">
                            <option value="">请选择点位</option>
                            <option value="1">A栋一楼大厅</option>
                            <option value="2">A栋二楼走廊</option>
                            <option value="3">B栋一楼大厅</option>
                            <option value="4">B栋二楼走廊</option>
                            <option value="5">C栋一楼大厅</option>
                            <option value="6">C栋二楼走廊</option>
                            <option value="7">数据中心机房</option>
                            <option value="8">停车场入口</option>
                            <option value="9">停车场出口</option>
                        </select>
                        <div class="form-hint">选择故障设备的具体位置</div>
                    </div>
                </div>

                <div class="form-section">
                    <div class="form-section-title">
                        <i class="fas fa-exclamation-circle"></i>
                        <span>故障信息</span>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="workorder-description">
                            故障描述
                        </label>
                        <textarea id="workorder-description" class="form-control form-textarea" placeholder="请详细描述故障情况..."></textarea>
                        <div class="form-hint">描述故障的现象、影响范围等信息</div>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="workorder-images">
                            故障图片
                            <span class="required-mark">*</span>
                        </label>
                        <div class="image-upload" id="workorder-images">
                            <div class="upload-box">
                                <i class="fas fa-plus"></i>
                                <span>上传图片</span>
                            </div>
                            <div class="image-preview">
                                <img src="https://via.placeholder.com/80x80/e0e0e0/808080?text=图片1" alt="预览图片">
                                <div class="remove-image">
                                    <i class="fas fa-times"></i>
                                </div>
                            </div>
                        </div>
                        <div class="form-hint">最多上传5张图片，每张图片大小不超过10M</div>
                    </div>
                </div>

                <div class="form-actions">
                    <button class="btn btn-secondary btn-block" onclick="closeModal()">取消</button>
                    <button class="btn btn-primary btn-block">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation Bar -->
    <div class="nav-bar">
        <a href="home.html" class="nav-item">
            <i class="nav-icon fas fa-home"></i>
            <span>首页</span>
        </a>
        <a href="modules.html" class="nav-item active">
            <i class="nav-icon fas fa-th-large"></i>
            <span>功能</span>
        </a>
        <a href="users.html" class="nav-item">
            <i class="nav-icon fas fa-cog"></i>
            <span>系统</span>
        </a>
        <a href="profile.html" class="nav-item">
            <i class="nav-icon fas fa-user"></i>
            <span>我的</span>
        </a>
    </div>

    <script>
        function switchTab(tabName) {
            // 隐藏所有内容
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            // 取消所有标签的选中状态
            document.querySelectorAll('.filter-tab').forEach(item => {
                item.classList.remove('active');
            });

            // 显示选中的内容
            document.getElementById(tabName + '-tab').classList.add('active');

            // 设置选中标签的样式
            event.currentTarget.classList.add('active');
        }

        function openModal() {
            document.getElementById('createWorkorderModal').classList.add('show');
            document.body.style.overflow = 'hidden'; // 防止背景滚动

            // 添加动画效果
            setTimeout(() => {
                const formContainer = document.querySelector('.form-container');
                if (formContainer) {
                    formContainer.classList.add('animate-fade-in');
                }
            }, 10);
        }

        function closeModal() {
            document.getElementById('createWorkorderModal').classList.remove('show');
            document.body.style.overflow = ''; // 恢复背景滚动
        }

        // 为保存按钮添加点击事件
        document.addEventListener('DOMContentLoaded', function() {
            const saveButton = document.querySelector('.btn-primary');
            if (saveButton) {
                saveButton.addEventListener('click', function() {
                    // 这里可以添加表单验证和提交逻辑
                    alert('工单已保存');
                    closeModal();
                });
            }

            // 为右上角的勾选图标添加点击事件
            const checkIcon = document.querySelector('.header-right .fa-check');
            if (checkIcon) {
                checkIcon.addEventListener('click', function() {
                    // 这里可以添加表单验证和提交逻辑
                    alert('工单已保存');
                    closeModal();
                });
            }
        });
    </script>
</body>
</html>
