<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新增角色 - 系统配置</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        .form-container {
            padding: 16px;
        }
        
        .form-section {
            margin-bottom: 24px;
        }
        
        .form-section-title {
            font-size: 17px;
            font-weight: 600;
            margin-bottom: 16px;
            color: var(--text-color);
            display: flex;
            align-items: center;
        }
        
        .form-section-title i {
            margin-right: 8px;
            color: var(--primary-color);
            font-size: 18px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            font-size: 15px;
            font-weight: 500;
        }
        
        .required-mark {
            color: var(--danger-color);
            margin-left: 4px;
        }
        
        .form-control {
            width: 100%;
            padding: 12px 16px;
            border-radius: 12px;
            border: 1px solid var(--medium-gray);
            font-size: 16px;
            background-color: var(--background-color);
            transition: all var(--transition-fast);
        }
        
        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
        }
        
        .form-select {
            appearance: none;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%238e8e93'%3E%3Cpath d='M7 10l5 5 5-5z'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 12px center;
            background-size: 20px;
            padding-right: 40px;
        }
        
        .form-textarea {
            min-height: 120px;
            resize: vertical;
        }
        
        .form-hint {
            font-size: 13px;
            color: var(--dark-gray);
            margin-top: 4px;
        }
        
        .form-actions {
            display: flex;
            gap: 12px;
            margin-top: 32px;
        }
        
        .btn-block {
            flex: 1;
        }
        
        .permission-group {
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            margin-bottom: 16px;
            overflow: hidden;
        }
        
        .permission-group-header {
            padding: 12px 16px;
            background-color: rgba(0, 122, 255, 0.05);
            display: flex;
            align-items: center;
            font-weight: 500;
            border-bottom: 1px solid var(--light-gray);
        }
        
        .permission-group-header label {
            margin-bottom: 0;
            margin-left: 8px;
            font-size: 15px;
        }
        
        .permission-group-items {
            padding: 8px 16px;
        }
        
        .permission-item {
            padding: 10px 0;
            display: flex;
            align-items: center;
            border-bottom: 1px solid var(--light-gray);
        }
        
        .permission-item:last-child {
            border-bottom: none;
        }
        
        .permission-item label {
            margin-bottom: 0;
            margin-left: 8px;
            font-weight: normal;
            font-size: 14px;
        }
        
        .checkbox-wrapper {
            display: flex;
            align-items: center;
        }
        
        .checkbox-wrapper input[type="checkbox"] {
            width: 20px;
            height: 20px;
            cursor: pointer;
        }
        
        .animate-fade-in {
            animation: fadeIn 0.3s ease-in-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <!-- Status Bar -->
    <div class="status-bar">
        <div>9:41</div>
        <div class="status-bar-icons">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>

    <!-- Header -->
    <div class="header">
        <div class="header-left">
            <a href="users.html" class="text-gray-600">
                <i class="fas fa-arrow-left"></i>
            </a>
        </div>
        <div class="header-title">新增角色</div>
        <div class="header-right">
            <i class="fas fa-check text-primary-500" onclick="saveRole()"></i>
        </div>
    </div>

    <!-- Content -->
    <div class="content">
        <div class="form-container animate-fade-in">
            <div class="form-section">
                <div class="form-section-title">
                    <i class="fas fa-info-circle"></i>
                    <span>基本信息</span>
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="role-name">
                        角色名称
                        <span class="required-mark">*</span>
                    </label>
                    <input type="text" id="role-name" class="form-control" placeholder="请输入角色名称">
                    <div class="form-hint">例如：系统管理员、运维人员、普通用户</div>
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="data-permission">
                        数据权限
                        <span class="required-mark">*</span>
                    </label>
                    <select id="data-permission" class="form-control form-select">
                        <option value="">请选择数据权限范围</option>
                        <option value="all">全部数据</option>
                        <option value="department-and-children">部门及子部门数据</option>
                        <option value="department">本部门数据</option>
                        <option value="personal">本人数据</option>
                    </select>
                    <div class="form-hint">决定用户可以访问的数据范围</div>
                </div>
            </div>
            
            <div class="form-section">
                <div class="form-section-title">
                    <i class="fas fa-key"></i>
                    <span>功能权限</span>
                </div>
                
                <div class="permission-group">
                    <div class="permission-group-header">
                        <div class="checkbox-wrapper">
                            <input type="checkbox" id="system-management" class="permission-group-checkbox">
                            <label for="system-management">系统管理</label>
                        </div>
                    </div>
                    <div class="permission-group-items">
                        <div class="permission-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="user-management" class="permission-item-checkbox">
                                <label for="user-management">用户管理</label>
                            </div>
                        </div>
                        <div class="permission-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="role-management" class="permission-item-checkbox">
                                <label for="role-management">角色管理</label>
                            </div>
                        </div>
                        <div class="permission-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="department-management" class="permission-item-checkbox">
                                <label for="department-management">部门管理</label>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="permission-group">
                    <div class="permission-group-header">
                        <div class="checkbox-wrapper">
                            <input type="checkbox" id="device-management" class="permission-group-checkbox">
                            <label for="device-management">设备管理</label>
                        </div>
                    </div>
                    <div class="permission-group-items">
                        <div class="permission-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="device-config" class="permission-item-checkbox">
                                <label for="device-config">设备配置</label>
                            </div>
                        </div>
                        <div class="permission-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="device-monitoring" class="permission-item-checkbox">
                                <label for="device-monitoring">设备监控</label>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="permission-group">
                    <div class="permission-group-header">
                        <div class="checkbox-wrapper">
                            <input type="checkbox" id="data-management" class="permission-group-checkbox">
                            <label for="data-management">数据管理</label>
                        </div>
                    </div>
                    <div class="permission-group-items">
                        <div class="permission-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="data-view" class="permission-item-checkbox">
                                <label for="data-view">数据查看</label>
                            </div>
                        </div>
                        <div class="permission-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="data-export" class="permission-item-checkbox">
                                <label for="data-export">数据导出</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="form-section">
                <div class="form-section-title">
                    <i class="fas fa-align-left"></i>
                    <span>其他信息</span>
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="role-description">
                        角色描述
                    </label>
                    <textarea id="role-description" class="form-control form-textarea" placeholder="请输入角色描述信息"></textarea>
                    <div class="form-hint">描述角色的用途、职责等信息</div>
                </div>
            </div>
            
            <div class="form-actions">
                <button class="btn btn-secondary btn-block" onclick="goBack()">取消</button>
                <button class="btn btn-primary btn-block" onclick="saveRole()">保存</button>
            </div>
        </div>
    </div>

    <!-- Navigation Bar -->
    <div class="nav-bar">
        <a href="home.html" class="nav-item">
            <i class="nav-icon fas fa-home"></i>
            <span>首页</span>
        </a>
        <a href="gateway.html" class="nav-item">
            <i class="nav-icon fas fa-th-large"></i>
            <span>功能</span>
        </a>
        <a href="users.html" class="nav-item active">
            <i class="nav-icon fas fa-cog"></i>
            <span>系统</span>
        </a>
        <a href="profile.html" class="nav-item">
            <i class="nav-icon fas fa-user"></i>
            <span>我的</span>
        </a>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            initPermissionCheckboxes();
        });
        
        function initPermissionCheckboxes() {
            // 为所有权限组复选框添加事件监听
            document.querySelectorAll('.permission-group-checkbox').forEach(groupCheckbox => {
                groupCheckbox.addEventListener('change', function() {
                    // 获取当前组内的所有子项复选框
                    const groupItems = this.closest('.permission-group').querySelectorAll('.permission-item-checkbox');
                    // 将子项复选框的状态与组复选框同步
                    groupItems.forEach(itemCheckbox => {
                        itemCheckbox.checked = this.checked;
                    });
                });
            });
            
            // 为所有权限子项复选框添加事件监听
            document.querySelectorAll('.permission-item-checkbox').forEach(itemCheckbox => {
                itemCheckbox.addEventListener('change', function() {
                    // 获取当前子项所属的组
                    const group = this.closest('.permission-group');
                    // 获取组内的所有子项复选框
                    const groupItems = group.querySelectorAll('.permission-item-checkbox');
                    // 获取组复选框
                    const groupCheckbox = group.querySelector('.permission-group-checkbox');
                    
                    // 检查组内是否所有子项都被选中
                    let allChecked = true;
                    groupItems.forEach(item => {
                        if (!item.checked) {
                            allChecked = false;
                        }
                    });
                    
                    // 更新组复选框状态
                    groupCheckbox.checked = allChecked;
                });
            });
        }
        
        function saveRole() {
            // 获取表单数据
            const roleName = document.getElementById('role-name').value.trim();
            const dataPermission = document.getElementById('data-permission').value;
            const roleDescription = document.getElementById('role-description').value.trim();
            
            // 表单验证
            if (!roleName) {
                alert('请输入角色名称');
                return;
            }
            
            if (!dataPermission) {
                alert('请选择数据权限');
                return;
            }
            
            // 获取选中的权限
            const selectedPermissions = [];
            document.querySelectorAll('.permission-item-checkbox:checked').forEach(checkbox => {
                selectedPermissions.push(checkbox.id);
            });
            
            // 模拟保存成功
            alert('角色添加成功');
            
            // 返回角色列表页面
            window.location.href = 'users.html';
        }
        
        function goBack() {
            window.location.href = 'users.html';
        }
    </script>
</body>
</html>
