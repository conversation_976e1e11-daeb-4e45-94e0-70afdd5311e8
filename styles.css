/* Common styles for the app */
:root {
    /* Primary Colors */
    --primary-color: #007aff;
    --primary-light: #64a8ff;
    --primary-dark: #0062cc;

    /* Secondary Colors */
    --secondary-color: #5ac8fa;
    --secondary-light: #8ad5fb;
    --secondary-dark: #28a7e1;

    /* Status Colors */
    --success-color: #34c759;
    --success-light: #86e2a1;
    --success-dark: #248a3d;

    --warning-color: #ff9500;
    --warning-light: #ffb340;
    --warning-dark: #c27400;

    --danger-color: #ff3b30;
    --danger-light: #ff6b62;
    --danger-dark: #d81e12;

    /* Neutral Colors */
    --light-gray: #f2f2f7;
    --medium-gray: #c7c7cc;
    --dark-gray: #8e8e93;

    /* Text and Background */
    --text-color: #000000;
    --text-secondary: #3c3c43;
    --background-color: #ffffff;
    --background-secondary: #f9f9f9;

    /* Shadows */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.15);

    /* Transitions */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: var(--background-secondary);
    color: var(--text-color);
    height: 100vh;
    width: 100%;
    overflow: hidden;
    line-height: 1.5;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Status Bar */
.status-bar {
    height: 44px;
    background-color: var(--background-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 16px;
    font-size: 12px;
    font-weight: 600;
    position: relative;
    z-index: 10;
}

.status-bar-time {
    flex: 1;
    text-align: center;
}

.status-bar-icons {
    display: flex;
    gap: 5px;
}

/* Navigation Bar */
.nav-bar {
    height: 83px;
    background-color: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    justify-content: space-around;
    align-items: center;
    padding-bottom: 20px;
    box-shadow: 0 -1px 10px rgba(0, 0, 0, 0.05);
    z-index: 100;
}

.nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: var(--dark-gray);
    text-decoration: none;
    font-size: 10px;
    transition: all var(--transition-fast);
    position: relative;
    padding: 6px 12px;
    border-radius: 8px;
}

.nav-item:active {
    background-color: rgba(0, 0, 0, 0.05);
    transform: scale(0.95);
}

.nav-item.active {
    color: var(--primary-color);
}

.nav-item.active::after {
    content: "";
    position: absolute;
    bottom: -6px;
    left: 50%;
    transform: translateX(-50%);
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: var(--primary-color);
}

.nav-icon {
    font-size: 22px;
    margin-bottom: 4px;
}

/* Header */
.header {
    height: 44px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    border-bottom: 1px solid var(--light-gray);
    background-color: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    z-index: 10;
    box-shadow: var(--shadow-sm);
}

.header-title {
    font-size: 17px;
    font-weight: 600;
    letter-spacing: -0.01em;
}

.header-left, .header-right {
    position: absolute;
    top: 0;
    height: 100%;
    display: flex;
    align-items: center;
    font-size: 17px;
}

.header-left {
    left: 16px;
}

.header-right {
    right: 16px;
}

/* Content Area */
.content {
    padding: 16px;
    overflow-y: auto;
    height: calc(100vh - 44px - 83px);
    -webkit-overflow-scrolling: touch;
}

/* Cards */
.card {
    background-color: var(--background-color);
    border-radius: 16px;
    box-shadow: var(--shadow-md);
    margin-bottom: 20px;
    overflow: hidden;
    border: 1px solid rgba(0, 0, 0, 0.03);
    transition: transform var(--transition-fast), box-shadow var(--transition-fast);
}

.card:active {
    transform: translateY(1px);
    box-shadow: var(--shadow-sm);
}

.card-header {
    padding: 16px;
    border-bottom: 1px solid var(--light-gray);
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-content {
    padding: 16px;
}

/* List Items */
.list-item {
    padding: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--light-gray);
}

.list-item:last-child {
    border-bottom: none;
}

.list-item-title {
    font-size: 17px;
}

.list-item-subtitle {
    font-size: 15px;
    color: var(--dark-gray);
    margin-top: 4px;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 24px;
    border-radius: 12px;
    font-size: 17px;
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-fast);
    border: none;
    outline: none;
    position: relative;
    overflow: hidden;
}

.btn:active {
    transform: translateY(1px) scale(0.98);
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
    box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
}

.btn-primary:active {
    background-color: var(--primary-dark);
    box-shadow: 0 1px 3px rgba(0, 122, 255, 0.2);
}

.btn-secondary {
    background-color: var(--light-gray);
    color: var(--primary-color);
}

.btn-secondary:active {
    background-color: #e5e5ea;
}

.btn-success {
    background-color: var(--success-color);
    color: white;
    box-shadow: 0 2px 8px rgba(52, 199, 89, 0.3);
}

.btn-success:active {
    background-color: var(--success-dark);
    box-shadow: 0 1px 3px rgba(52, 199, 89, 0.2);
}

.btn-warning {
    background-color: var(--warning-color);
    color: white;
    box-shadow: 0 2px 8px rgba(255, 149, 0, 0.3);
}

.btn-warning:active {
    background-color: var(--warning-dark);
    box-shadow: 0 1px 3px rgba(255, 149, 0, 0.2);
}

.btn-danger {
    background-color: var(--danger-color);
    color: white;
    box-shadow: 0 2px 8px rgba(255, 59, 48, 0.3);
}

.btn-danger:active {
    background-color: var(--danger-dark);
    box-shadow: 0 1px 3px rgba(255, 59, 48, 0.2);
}

.btn-block {
    display: block;
    width: 100%;
}

/* Form Elements */
.form-group {
    margin-bottom: 16px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    font-size: 15px;
    font-weight: 500;
}

.form-control {
    width: 100%;
    padding: 12px 16px;
    border-radius: 10px;
    border: 1px solid var(--medium-gray);
    font-size: 17px;
    background-color: var(--background-color);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
}

/* Badges */
.badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 100px;
    font-size: 12px;
    font-weight: 600;
}

.badge-primary {
    background-color: var(--primary-color);
    color: white;
}

.badge-success {
    background-color: var(--success-color);
    color: white;
}

.badge-warning {
    background-color: var(--warning-color);
    color: white;
}

.badge-danger {
    background-color: var(--danger-color);
    color: white;
}

/* Animations */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* Utility Classes */
.animate-pulse {
    animation: pulse 2s infinite;
}

.animate-fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

.animate-slide-up {
    animation: slideUp 0.3s ease-out;
}

/* Empty State */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    text-align: center;
}

.empty-state-icon {
    width: 80px;
    height: 80px;
    border-radius: 40px;
    background-color: var(--light-gray);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32px;
    color: var(--dark-gray);
    margin-bottom: 16px;
}

.empty-state-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
}

.empty-state-description {
    font-size: 15px;
    color: var(--dark-gray);
    margin-bottom: 20px;
    line-height: 1.4;
}

/* Skeleton Loading */
.skeleton {
    background: linear-gradient(90deg, var(--light-gray) 25%, var(--background-secondary) 50%, var(--light-gray) 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: 4px;
}

@keyframes skeleton-loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

.skeleton-text {
    height: 16px;
    margin-bottom: 8px;
    width: 100%;
}

.skeleton-text:last-child {
    width: 80%;
}

.skeleton-circle {
    width: 48px;
    height: 48px;
    border-radius: 50%;
}

.skeleton-rectangle {
    width: 100%;
    height: 120px;
    border-radius: 8px;
}
