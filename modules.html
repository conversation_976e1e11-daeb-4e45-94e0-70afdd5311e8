<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>功能模块 - 项目管理平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        .module-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 16px;
            padding: 16px;
        }

        .module-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            background-color: white;
            border-radius: 12px;
            padding: 20px 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .module-item:active {
            transform: scale(0.98);
            box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
        }

        .module-icon {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 12px;
            font-size: 28px;
            color: white;
        }

        .module-name {
            font-size: 14px;
            font-weight: 600;
            color: var(--text-color);
        }

        .module-description {
            font-size: 12px;
            color: var(--dark-gray);
            margin-top: 4px;
            line-height: 1.3;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            margin: 24px 0 12px 16px;
        }

        .bg-gateway {
            background: linear-gradient(135deg, #007aff, #5ac8fa);
        }

        .bg-device {
            background: linear-gradient(135deg, #34c759, #32d74b);
        }

        .bg-rules {
            background: linear-gradient(135deg, #ff9500, #ffcc00);
        }

        .bg-tasks {
            background: linear-gradient(135deg, #ff3b30, #ff6b6b);
        }

        .bg-space {
            background: linear-gradient(135deg, #5856d6, #af52de);
        }

        .bg-alerts {
            background: linear-gradient(135deg, #ff2d55, #ff6482);
        }

        .bg-workorders {
            background: linear-gradient(135deg, #a2845e, #d4b48c);
        }

        .bg-scada {
            background: linear-gradient(135deg, #64d2ff, #5ac8fa);
        }

        .bg-patrol {
            background: linear-gradient(135deg, #3498db, #2980b9);
        }

        .bg-video {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }

        /* 项目管理相关样式 */
        .bg-projects {
            background: linear-gradient(135deg, #1abc9c, #16a085);
        }

        .bg-tasks-management {
            background: linear-gradient(135deg, #9b59b6, #8e44ad);
        }

        .bg-team {
            background: linear-gradient(135deg, #3498db, #2980b9);
        }

        .bg-calendar {
            background: linear-gradient(135deg, #f1c40f, #f39c12);
        }

        .bg-documents {
            background: linear-gradient(135deg, #95a5a6, #7f8c8d);
        }

        .bg-analytics {
            background: linear-gradient(135deg, #2ecc71, #27ae60);
        }

        .bg-messages {
            background: linear-gradient(135deg, #e67e22, #d35400);
        }

        .bg-settings {
            background: linear-gradient(135deg, #34495e, #2c3e50);
        }

        .recent-module {
            display: flex;
            align-items: center;
            background-color: white;
            border-radius: 12px;
            padding: 12px;
            margin: 0 16px 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .recent-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 20px;
            color: white;
        }

        .recent-info {
            flex: 1;
        }

        .recent-name {
            font-size: 15px;
            font-weight: 600;
        }

        .recent-time {
            font-size: 12px;
            color: var(--dark-gray);
        }

        a {
            text-decoration: none;
            color: inherit;
        }
    </style>
</head>
<body>
    <!-- Status Bar -->
    <div class="status-bar">
        <div>9:41</div>
        <div class="status-bar-icons">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>

    <!-- Header -->
    <div class="header">
        <div class="header-title">功能模块</div>
        <div class="header-right">
            <i class="fas fa-search text-gray-600"></i>
        </div>
    </div>

    <!-- Content -->
    <div class="content">
        <!-- Recent Used -->
        <div class="section-title">最近使用</div>

        <a href="#" class="recent-module">
            <div class="recent-icon bg-projects">
                <i class="fas fa-project-diagram"></i>
            </div>
            <div class="recent-info">
                <div class="recent-name">项目管理</div>
                <div class="recent-time">刚刚</div>
            </div>
            <i class="fas fa-chevron-right text-gray-400"></i>
        </a>

        <a href="#" class="recent-module">
            <div class="recent-icon bg-tasks-management">
                <i class="fas fa-tasks"></i>
            </div>
            <div class="recent-info">
                <div class="recent-name">任务管理</div>
                <div class="recent-time">5分钟前</div>
            </div>
            <i class="fas fa-chevron-right text-gray-400"></i>
        </a>

        <a href="#" class="recent-module">
            <div class="recent-icon bg-team">
                <i class="fas fa-users"></i>
            </div>
            <div class="recent-info">
                <div class="recent-name">团队协作</div>
                <div class="recent-time">今天 10:30</div>
            </div>
            <i class="fas fa-chevron-right text-gray-400"></i>
        </a>

        <a href="video.html" class="recent-module">
            <div class="recent-icon bg-video">
                <i class="fas fa-video"></i>
            </div>
            <div class="recent-info">
                <div class="recent-name">监控视频管理</div>
                <div class="recent-time">今天 09:15</div>
            </div>
            <i class="fas fa-chevron-right text-gray-400"></i>
        </a>

        <a href="gateway.html" class="recent-module">
            <div class="recent-icon bg-gateway">
                <i class="fas fa-network-wired"></i>
            </div>
            <div class="recent-info">
                <div class="recent-name">网关管理</div>
                <div class="recent-time">昨天 16:20</div>
            </div>
            <i class="fas fa-chevron-right text-gray-400"></i>
        </a>

        <!-- All Modules -->
        <div class="section-title">全部功能</div>

        <div class="module-grid">
            <!-- 项目管理功能 -->
            <a href="#" class="module-item">
                <div class="module-icon bg-projects">
                    <i class="fas fa-project-diagram"></i>
                </div>
                <div class="module-name">项目管理</div>
                <div class="module-description">创建和管理项目</div>
            </a>

            <a href="#" class="module-item">
                <div class="module-icon bg-tasks-management">
                    <i class="fas fa-tasks"></i>
                </div>
                <div class="module-name">任务管理</div>
                <div class="module-description">分配和跟踪任务</div>
            </a>

            <a href="#" class="module-item">
                <div class="module-icon bg-team">
                    <i class="fas fa-users"></i>
                </div>
                <div class="module-name">团队协作</div>
                <div class="module-description">团队成员和权限</div>
            </a>

            <a href="#" class="module-item">
                <div class="module-icon bg-calendar">
                    <i class="fas fa-calendar-alt"></i>
                </div>
                <div class="module-name">项目日程</div>
                <div class="module-description">日程安排和里程碑</div>
            </a>

            <a href="#" class="module-item">
                <div class="module-icon bg-documents">
                    <i class="fas fa-file-alt"></i>
                </div>
                <div class="module-name">文档管理</div>
                <div class="module-description">项目文档和资料</div>
            </a>

            <a href="#" class="module-item">
                <div class="module-icon bg-analytics">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="module-name">数据分析</div>
                <div class="module-description">项目数据和报表</div>
            </a>

            <a href="#" class="module-item">
                <div class="module-icon bg-messages">
                    <i class="fas fa-comments"></i>
                </div>
                <div class="module-name">消息中心</div>
                <div class="module-description">团队沟通和通知</div>
            </a>

            <a href="#" class="module-item">
                <div class="module-icon bg-settings">
                    <i class="fas fa-cog"></i>
                </div>
                <div class="module-name">系统设置</div>
                <div class="module-description">平台配置和权限</div>
            </a>

            <!-- 设备管理功能 -->
            <a href="gateway.html" class="module-item">
                <div class="module-icon bg-gateway">
                    <i class="fas fa-network-wired"></i>
                </div>
                <div class="module-name">网关管理</div>
                <div class="module-description">管理所有网关设备</div>
            </a>

            <a href="device.html" class="module-item">
                <div class="module-icon bg-device">
                    <i class="fas fa-microchip"></i>
                </div>
                <div class="module-name">设备配置</div>
                <div class="module-description">设备类型、分组和管理</div>
            </a>

            <a href="video.html" class="module-item">
                <div class="module-icon bg-video">
                    <i class="fas fa-video"></i>
                </div>
                <div class="module-name">监控视频管理</div>
                <div class="module-description">实时监控和录像回放</div>
            </a>

            <a href="patrol.html" class="module-item">
                <div class="module-icon bg-patrol">
                    <i class="fas fa-route"></i>
                </div>
                <div class="module-name">巡更管理</div>
                <div class="module-description">巡检任务和路线管理</div>
            </a>
        </div>
    </div>

    <!-- Navigation Bar -->
    <div class="nav-bar">
        <a href="home.html" class="nav-item">
            <i class="nav-icon fas fa-home"></i>
            <span>首页</span>
        </a>
        <a href="modules.html" class="nav-item active">
            <i class="nav-icon fas fa-th-large"></i>
            <span>功能</span>
        </a>
        <a href="users.html" class="nav-item">
            <i class="nav-icon fas fa-cog"></i>
            <span>系统</span>
        </a>
        <a href="profile.html" class="nav-item">
            <i class="nav-icon fas fa-user"></i>
            <span>我的</span>
        </a>
    </div>
</body>
</html>
