<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>报警配置 - 项目管理平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        .search-bar {
            display: flex;
            align-items: center;
            background-color: var(--light-gray);
            border-radius: 10px;
            padding: 8px 12px;
            margin-bottom: 16px;
        }

        .search-bar input {
            flex: 1;
            border: none;
            background: transparent;
            margin-left: 8px;
            font-size: 15px;
        }

        .search-bar input:focus {
            outline: none;
        }

        .segment-control {
            display: flex;
            background-color: var(--light-gray);
            border-radius: 10px;
            padding: 4px;
            margin-bottom: 20px;
        }

        .segment-item {
            flex: 1;
            text-align: center;
            padding: 8px 0;
            font-size: 14px;
            border-radius: 8px;
        }

        .segment-item.active {
            background-color: white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            font-weight: 600;
        }

        .alert-card {
            background-color: white;
            border-radius: 12px;
            margin-bottom: 16px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            overflow: hidden;
        }

        .alert-header {
            padding: 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid var(--light-gray);
        }

        .alert-title {
            display: flex;
            align-items: center;
        }

        .alert-icon {
            width: 36px;
            height: 36px;
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 16px;
            color: white;
        }

        .alert-icon.critical {
            background-color: var(--danger-color);
        }

        .alert-icon.warning {
            background-color: var(--warning-color);
        }

        .alert-icon.info {
            background-color: var(--primary-color);
        }

        .alert-name {
            font-size: 17px;
            font-weight: 600;
        }

        .alert-time {
            font-size: 14px;
            color: var(--dark-gray);
        }

        .alert-content {
            padding: 16px;
        }

        .alert-description {
            font-size: 15px;
            color: var(--dark-gray);
            margin-bottom: 12px;
        }

        .alert-details {
            background-color: var(--light-gray);
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 16px;
        }

        .alert-detail-item {
            display: flex;
            margin-bottom: 8px;
        }

        .alert-detail-item:last-child {
            margin-bottom: 0;
        }

        .detail-label {
            width: 80px;
            font-size: 14px;
            color: var(--dark-gray);
        }

        .detail-value {
            flex: 1;
            font-size: 14px;
        }

        .alert-actions {
            display: flex;
            gap: 8px;
        }

        .alert-action-btn {
            flex: 1;
            padding: 8px 0;
            border-radius: 8px;
            font-size: 13px;
            text-align: center;
            background-color: var(--light-gray);
            color: var(--dark-gray);
        }

        .alert-action-btn.primary {
            background-color: rgba(0, 122, 255, 0.1);
            color: var(--primary-color);
        }

        .filter-tabs {
            display: flex;
            overflow-x: auto;
            margin-bottom: 16px;
            -webkit-overflow-scrolling: touch;
        }

        .filter-tab {
            padding: 8px 16px;
            border-radius: 100px;
            font-size: 14px;
            white-space: nowrap;
            margin-right: 8px;
        }

        .filter-tab.active {
            background-color: var(--primary-color);
            color: white;
        }

        .filter-tab:not(.active) {
            background-color: var(--light-gray);
            color: var(--dark-gray);
        }

        /* 通知组管理样式 */
        .notify-group-card {
            background-color: white;
            border-radius: 12px;
            margin-bottom: 16px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            padding: 16px;
        }

        .notify-group-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .notify-group-name {
            font-size: 16px;
            font-weight: 600;
        }

        .notify-group-toggle {
            position: relative;
            display: inline-block;
            width: 44px;
            height: 24px;
        }

        .notify-group-toggle input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: var(--light-gray);
            transition: .4s;
            border-radius: 24px;
        }

        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 20px;
            width: 20px;
            left: 2px;
            bottom: 2px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .toggle-slider {
            background-color: var(--primary-color);
        }

        input:checked + .toggle-slider:before {
            transform: translateX(20px);
        }

        .notify-group-members {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 12px;
        }

        .member-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: var(--light-gray);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--dark-gray);
            font-size: 14px;
            font-weight: 600;
        }

        .member-add {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: rgba(0, 122, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-color);
            font-size: 14px;
        }

        .notify-group-channels {
            display: flex;
            gap: 8px;
            margin-bottom: 12px;
        }

        .channel-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            background-color: var(--light-gray);
            color: var(--dark-gray);
        }

        .notify-group-actions {
            display: flex;
            justify-content: flex-end;
            gap: 8px;
        }

        .group-action-btn {
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 13px;
            background-color: var(--light-gray);
            color: var(--dark-gray);
        }

        .group-action-btn.primary {
            background-color: rgba(0, 122, 255, 0.1);
            color: var(--primary-color);
        }

        /* 报警规则样式 */
        .rule-card {
            background-color: white;
            border-radius: 12px;
            margin-bottom: 16px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            overflow: hidden;
        }

        .rule-header {
            padding: 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid var(--light-gray);
        }

        .rule-name {
            font-size: 16px;
            font-weight: 600;
        }

        .rule-toggle {
            position: relative;
            display: inline-block;
            width: 44px;
            height: 24px;
        }

        .rule-toggle input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .rule-content {
            padding: 16px;
        }

        .rule-description {
            font-size: 14px;
            color: var(--dark-gray);
            margin-bottom: 12px;
        }

        .rule-conditions {
            background-color: var(--light-gray);
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 12px;
        }

        .condition-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .condition-item:last-child {
            margin-bottom: 0;
        }

        .condition-operator {
            color: var(--primary-color);
            font-weight: 600;
            margin: 0 4px;
        }

        .rule-actions {
            display: flex;
            gap: 8px;
        }

        .rule-action-btn {
            flex: 1;
            padding: 8px 0;
            border-radius: 8px;
            font-size: 13px;
            text-align: center;
            background-color: var(--light-gray);
            color: var(--dark-gray);
        }

        .rule-action-btn.primary {
            background-color: rgba(0, 122, 255, 0.1);
            color: var(--primary-color);
        }

        .rule-severity {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .rule-severity.critical {
            background-color: rgba(255, 45, 85, 0.1);
            color: var(--danger-color);
        }

        .rule-severity.warning {
            background-color: rgba(255, 149, 0, 0.1);
            color: var(--warning-color);
        }

        .rule-severity.info {
            background-color: rgba(0, 122, 255, 0.1);
            color: var(--primary-color);
        }

        /* 标签内容区域 */
        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* 浮动添加按钮样式 */
        .add-button {
            position: fixed;
            bottom: 100px;
            right: 20px;
            width: 56px;
            height: 56px;
            border-radius: 50%;
            background-color: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
            z-index: 50;
            transition: all var(--transition-fast);
        }

        .add-button:active {
            transform: scale(0.95);
            box-shadow: 0 2px 8px rgba(0, 122, 255, 0.2);
        }
    </style>
</head>
<body>
    <!-- Status Bar -->
    <div class="status-bar">
        <div>9:41</div>
        <div class="status-bar-icons">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>

    <!-- Header -->
    <div class="header">
        <div class="header-left">
            <a href="modules.html" class="text-gray-600">
                <i class="fas fa-arrow-left"></i>
            </a>
        </div>
        <div class="header-title">报警配置</div>
        <div class="header-right">
            <i class="fas fa-search text-gray-600"></i>
        </div>
    </div>

    <!-- Content -->
    <div class="content">
        <!-- Search Bar -->
        <div class="search-bar">
            <i class="fas fa-search text-gray-400"></i>
            <input type="text" placeholder="搜索报警...">
        </div>

        <!-- Segment Control -->
        <div class="segment-control">
            <div class="segment-item" onclick="switchTab('notify')">通知组管理</div>
            <div class="segment-item" onclick="switchTab('rules')">报警规则</div>
            <div class="segment-item active" onclick="switchTab('alerts')">报警记录</div>
        </div>

        <!-- 通知组管理标签内容 -->
        <div id="notify-tab" class="tab-content">
            <div class="notify-group-card">
                <div class="notify-group-header">
                    <div class="notify-group-name">运维团队</div>
                    <label class="notify-group-toggle">
                        <input type="checkbox" checked>
                        <span class="toggle-slider"></span>
                    </label>
                </div>
                <div class="notify-group-members">
                    <div class="member-avatar" style="background-color: #FF9500; color: white;">ZL</div>
                    <div class="member-avatar" style="background-color: #34C759; color: white;">WJ</div>
                    <div class="member-avatar" style="background-color: #5856D6; color: white;">LM</div>
                    <div class="member-avatar" style="background-color: #FF2D55; color: white;">CY</div>
                    <div class="member-add">
                        <i class="fas fa-plus"></i>
                    </div>
                </div>
                <div class="notify-group-channels">
                    <div class="channel-badge">短信</div>
                    <div class="channel-badge">邮件</div>
                    <div class="channel-badge">微信</div>
                    <div class="channel-badge">电话</div>
                </div>
                <div class="notify-group-actions">
                    <div class="group-action-btn">删除</div>
                    <div class="group-action-btn primary">编辑</div>
                </div>
            </div>

            <div class="notify-group-card">
                <div class="notify-group-header">
                    <div class="notify-group-name">管理层</div>
                    <label class="notify-group-toggle">
                        <input type="checkbox" checked>
                        <span class="toggle-slider"></span>
                    </label>
                </div>
                <div class="notify-group-members">
                    <div class="member-avatar" style="background-color: #007AFF; color: white;">ZS</div>
                    <div class="member-avatar" style="background-color: #FF9500; color: white;">LJ</div>
                    <div class="member-add">
                        <i class="fas fa-plus"></i>
                    </div>
                </div>
                <div class="notify-group-channels">
                    <div class="channel-badge">短信</div>
                    <div class="channel-badge">邮件</div>
                </div>
                <div class="notify-group-actions">
                    <div class="group-action-btn">删除</div>
                    <div class="group-action-btn primary">编辑</div>
                </div>
            </div>

            <div class="notify-group-card">
                <div class="notify-group-header">
                    <div class="notify-group-name">安保团队</div>
                    <label class="notify-group-toggle">
                        <input type="checkbox">
                        <span class="toggle-slider"></span>
                    </label>
                </div>
                <div class="notify-group-members">
                    <div class="member-avatar" style="background-color: #34C759; color: white;">WL</div>
                    <div class="member-avatar" style="background-color: #5856D6; color: white;">ZH</div>
                    <div class="member-avatar" style="background-color: #FF2D55; color: white;">LK</div>
                    <div class="member-add">
                        <i class="fas fa-plus"></i>
                    </div>
                </div>
                <div class="notify-group-channels">
                    <div class="channel-badge">短信</div>
                    <div class="channel-badge">电话</div>
                </div>
                <div class="notify-group-actions">
                    <div class="group-action-btn">删除</div>
                    <div class="group-action-btn primary">编辑</div>
                </div>
            </div>
        </div>

        <!-- 报警规则标签内容 -->
        <div id="rules-tab" class="tab-content">
            <div class="rule-card">
                <div class="rule-header">
                    <div class="rule-name">温度过高告警</div>
                    <label class="rule-toggle">
                        <input type="checkbox" checked>
                        <span class="toggle-slider"></span>
                    </label>
                </div>
                <div class="rule-content">
                    <div class="rule-description">
                        监测服务器机房温度，当温度超过阈值时触发告警
                    </div>
                    <div class="rule-conditions">
                        <div class="condition-item">
                            <span>温度</span>
                            <span class="condition-operator">></span>
                            <span>28°C</span>
                        </div>
                        <div class="condition-item">
                            <span>持续时间</span>
                            <span class="condition-operator">></span>
                            <span>5分钟</span>
                        </div>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-bottom: 12px;">
                        <div class="rule-severity critical">紧急</div>
                        <div style="font-size: 13px; color: var(--dark-gray);">通知组: 运维团队</div>
                    </div>
                    <div class="rule-actions">
                        <div class="rule-action-btn">
                            <i class="fas fa-history mr-1"></i> 历史记录
                        </div>
                        <div class="rule-action-btn primary">
                            <i class="fas fa-edit mr-1"></i> 编辑规则
                        </div>
                    </div>
                </div>
            </div>

            <div class="rule-card">
                <div class="rule-header">
                    <div class="rule-name">设备离线告警</div>
                    <label class="rule-toggle">
                        <input type="checkbox" checked>
                        <span class="toggle-slider"></span>
                    </label>
                </div>
                <div class="rule-content">
                    <div class="rule-description">
                        监测网关设备在线状态，当设备离线超过指定时间时触发告警
                    </div>
                    <div class="rule-conditions">
                        <div class="condition-item">
                            <span>设备状态</span>
                            <span class="condition-operator">=</span>
                            <span>离线</span>
                        </div>
                        <div class="condition-item">
                            <span>持续时间</span>
                            <span class="condition-operator">></span>
                            <span>30分钟</span>
                        </div>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-bottom: 12px;">
                        <div class="rule-severity warning">警告</div>
                        <div style="font-size: 13px; color: var(--dark-gray);">通知组: 运维团队</div>
                    </div>
                    <div class="rule-actions">
                        <div class="rule-action-btn">
                            <i class="fas fa-history mr-1"></i> 历史记录
                        </div>
                        <div class="rule-action-btn primary">
                            <i class="fas fa-edit mr-1"></i> 编辑规则
                        </div>
                    </div>
                </div>
            </div>

            <div class="rule-card">
                <div class="rule-header">
                    <div class="rule-name">能耗异常提醒</div>
                    <label class="rule-toggle">
                        <input type="checkbox" checked>
                        <span class="toggle-slider"></span>
                    </label>
                </div>
                <div class="rule-content">
                    <div class="rule-description">
                        监测建筑能耗数据，当能耗较昨日同期增长超过阈值时触发提醒
                    </div>
                    <div class="rule-conditions">
                        <div class="condition-item">
                            <span>能耗增长率</span>
                            <span class="condition-operator">></span>
                            <span>20%</span>
                        </div>
                        <div class="condition-item">
                            <span>比较周期</span>
                            <span class="condition-operator">=</span>
                            <span>昨日同期</span>
                        </div>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-bottom: 12px;">
                        <div class="rule-severity info">信息</div>
                        <div style="font-size: 13px; color: var(--dark-gray);">通知组: 管理层</div>
                    </div>
                    <div class="rule-actions">
                        <div class="rule-action-btn">
                            <i class="fas fa-history mr-1"></i> 历史记录
                        </div>
                        <div class="rule-action-btn primary">
                            <i class="fas fa-edit mr-1"></i> 编辑规则
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 报警记录标签内容 -->
        <div id="alerts-tab" class="tab-content active">
            <!-- Filter Tabs -->
            <div class="filter-tabs">
                <div class="filter-tab active">全部</div>
                <div class="filter-tab">紧急</div>
                <div class="filter-tab">警告</div>
                <div class="filter-tab">信息</div>
                <div class="filter-tab">已处理</div>
                <div class="filter-tab">未处理</div>
            </div>
        </div>

        <!-- Alert List -->
        <div class="alert-card">
            <div class="alert-header">
                <div class="alert-title">
                    <div class="alert-icon critical">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div>
                        <div class="alert-name">温度过高告警</div>
                        <div class="alert-time">今天 10:25</div>
                    </div>
                </div>
                <div class="badge badge-danger">紧急</div>
            </div>
            <div class="alert-content">
                <div class="alert-description">
                    服务器机房温度超过阈值，当前温度 32°C，阈值 28°C
                </div>
                <div class="alert-details">
                    <div class="alert-detail-item">
                        <div class="detail-label">设备:</div>
                        <div class="detail-value">温度传感器 TS-001</div>
                    </div>
                    <div class="alert-detail-item">
                        <div class="detail-label">位置:</div>
                        <div class="detail-value">E栋数据中心 - 1楼</div>
                    </div>
                    <div class="alert-detail-item">
                        <div class="detail-label">状态:</div>
                        <div class="detail-value text-red-500">未处理</div>
                    </div>
                </div>
                <div class="alert-actions">
                    <div class="alert-action-btn">
                        <i class="fas fa-check-circle mr-1"></i> 标记已处理
                    </div>
                    <div class="alert-action-btn primary">
                        <i class="fas fa-clipboard-list mr-1"></i> 创建工单
                    </div>
                </div>
            </div>
        </div>

        <div class="alert-card">
            <div class="alert-header">
                <div class="alert-title">
                    <div class="alert-icon warning">
                        <i class="fas fa-exclamation-circle"></i>
                    </div>
                    <div>
                        <div class="alert-name">设备离线告警</div>
                        <div class="alert-time">今天 09:15</div>
                    </div>
                </div>
                <div class="badge badge-warning">警告</div>
            </div>
            <div class="alert-content">
                <div class="alert-description">
                    网关设备 GW-003 已离线超过30分钟
                </div>
                <div class="alert-details">
                    <div class="alert-detail-item">
                        <div class="detail-label">设备:</div>
                        <div class="detail-value">网关 GW-003</div>
                    </div>
                    <div class="alert-detail-item">
                        <div class="detail-label">位置:</div>
                        <div class="detail-value">西区网关</div>
                    </div>
                    <div class="alert-detail-item">
                        <div class="detail-label">状态:</div>
                        <div class="detail-value text-green-500">已处理</div>
                    </div>
                </div>
                <div class="alert-actions">
                    <div class="alert-action-btn">
                        <i class="fas fa-undo mr-1"></i> 重新打开
                    </div>
                    <div class="alert-action-btn primary">
                        <i class="fas fa-info-circle mr-1"></i> 查看详情
                    </div>
                </div>
            </div>
        </div>

        <div class="alert-card">
            <div class="alert-header">
                <div class="alert-title">
                    <div class="alert-icon info">
                        <i class="fas fa-info-circle"></i>
                    </div>
                    <div>
                        <div class="alert-name">能耗异常提醒</div>
                        <div class="alert-time">昨天 15:40</div>
                    </div>
                </div>
                <div class="badge badge-primary">信息</div>
            </div>
            <div class="alert-content">
                <div class="alert-description">
                    A栋办公楼能耗较昨日同期增长 25%
                </div>
                <div class="alert-details">
                    <div class="alert-detail-item">
                        <div class="detail-label">设备:</div>
                        <div class="detail-value">能耗监测系统</div>
                    </div>
                    <div class="alert-detail-item">
                        <div class="detail-label">位置:</div>
                        <div class="detail-value">A栋办公楼</div>
                    </div>
                    <div class="alert-detail-item">
                        <div class="detail-label">状态:</div>
                        <div class="detail-value text-red-500">未处理</div>
                    </div>
                </div>
                <div class="alert-actions">
                    <div class="alert-action-btn">
                        <i class="fas fa-check-circle mr-1"></i> 标记已处理
                    </div>
                    <div class="alert-action-btn primary">
                        <i class="fas fa-chart-line mr-1"></i> 查看能耗
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加按钮 -->
    <a href="#" class="add-button animate-pulse">
        <i class="fas fa-plus"></i>
    </a>

    <!-- Navigation Bar -->
    <div class="nav-bar">
        <a href="home.html" class="nav-item">
            <i class="nav-icon fas fa-home"></i>
            <span>首页</span>
        </a>
        <a href="modules.html" class="nav-item active">
            <i class="nav-icon fas fa-th-large"></i>
            <span>功能</span>
        </a>
        <a href="users.html" class="nav-item">
            <i class="nav-icon fas fa-cog"></i>
            <span>系统</span>
        </a>
        <a href="profile.html" class="nav-item">
            <i class="nav-icon fas fa-user"></i>
            <span>我的</span>
        </a>
    </div>

    <script>
        function switchTab(tabName) {
            // 隐藏所有内容
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            // 取消所有标签的选中状态
            document.querySelectorAll('.segment-item').forEach(item => {
                item.classList.remove('active');
            });

            // 显示选中的内容
            document.getElementById(tabName + '-tab').classList.add('active');

            // 设置选中标签的样式
            event.currentTarget.classList.add('active');
        }
    </script>
</body>
</html>
