<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>忘记密码 - 项目管理平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        .forgot-container {
            display: flex;
            flex-direction: column;
            justify-content: center;
            height: 100vh;
            padding: 0 24px;
            background-color: #ffffff;
            background-image: url('https://images.unsplash.com/photo-1522071820081-009f0129c71c?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80');
            background-size: cover;
            background-position: center;
            position: relative;
        }

        .forgot-container::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255, 255, 255, 0.85);
            z-index: 1;
        }

        .forgot-content {
            position: relative;
            z-index: 2;
        }

        .forgot-logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 32px;
            background-color: var(--primary-color);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 40px;
            box-shadow: 0 8px 20px rgba(0, 122, 255, 0.3);
        }

        .forgot-title {
            text-align: center;
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 16px;
            color: #333;
        }

        .forgot-subtitle {
            text-align: center;
            font-size: 16px;
            color: #666;
            margin-bottom: 32px;
        }

        .forgot-form {
            background-color: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
        }

        .verification-input {
            display: flex;
            gap: 8px;
            justify-content: center;
            margin: 20px 0;
        }

        .verification-digit {
            width: 50px;
            height: 50px;
            border: 2px solid var(--light-gray);
            border-radius: 8px;
            text-align: center;
            font-size: 20px;
            font-weight: 600;
            transition: all 0.2s ease;
        }

        .verification-digit:focus {
            border-color: var(--primary-color);
            outline: none;
            box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
        }

        .countdown-btn {
            background-color: var(--light-gray);
            color: var(--dark-gray);
            border: none;
            padding: 12px 16px;
            border-radius: 8px;
            font-size: 14px;
            cursor: not-allowed;
            transition: all 0.2s ease;
        }

        .countdown-btn.active {
            background-color: var(--primary-color);
            color: white;
            cursor: pointer;
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 24px;
        }

        .step {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background-color: var(--light-gray);
            color: var(--dark-gray);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin: 0 8px;
            position: relative;
        }

        .step.active {
            background-color: var(--primary-color);
            color: white;
        }

        .step.completed {
            background-color: var(--success-color);
            color: white;
        }

        .step::after {
            content: "";
            position: absolute;
            top: 50%;
            left: 100%;
            width: 16px;
            height: 2px;
            background-color: var(--light-gray);
            transform: translateY(-50%);
        }

        .step:last-child::after {
            display: none;
        }

        .step.completed::after {
            background-color: var(--success-color);
        }

        .animate-fade-in {
            animation: fadeIn 0.3s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <!-- Status Bar -->
    <div class="status-bar">
        <div>9:41</div>
        <div class="status-bar-icons">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>

    <div class="forgot-container">
        <div class="forgot-content">
            <!-- 返回按钮 -->
            <div class="mb-4">
                <a href="login.html" class="text-gray-600 text-lg">
                    <i class="fas fa-arrow-left"></i>
                </a>
            </div>

            <div class="forgot-logo">
                <i class="fas fa-key"></i>
            </div>
            <h1 class="forgot-title">忘记密码</h1>
            <p class="forgot-subtitle">请按照以下步骤重置您的密码</p>

            <!-- 步骤指示器 -->
            <div class="step-indicator">
                <div class="step active" id="step1">1</div>
                <div class="step" id="step2">2</div>
                <div class="step" id="step3">3</div>
            </div>

            <div class="forgot-form animate-fade-in">
                <!-- 步骤1: 输入手机号 -->
                <div id="phone-step" class="step-content">
                    <div class="form-group">
                        <label class="form-label" for="phone">手机号码</label>
                        <div class="relative">
                            <span class="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500">
                                <i class="fas fa-mobile-alt"></i>
                            </span>
                            <input type="tel" id="phone" class="form-control pl-10" placeholder="请输入手机号码" maxlength="11">
                        </div>
                        <div class="text-sm text-gray-500 mt-2">
                            我们将向您的手机发送验证码
                        </div>
                    </div>

                    <button class="btn btn-primary btn-block" onclick="sendVerificationCode()">发送验证码</button>
                </div>

                <!-- 步骤2: 验证码验证 -->
                <div id="verification-step" class="step-content" style="display: none;">
                    <div class="form-group">
                        <label class="form-label">验证码</label>
                        <div class="verification-input">
                            <input type="text" class="verification-digit" maxlength="1" oninput="moveToNext(this, 0)">
                            <input type="text" class="verification-digit" maxlength="1" oninput="moveToNext(this, 1)">
                            <input type="text" class="verification-digit" maxlength="1" oninput="moveToNext(this, 2)">
                            <input type="text" class="verification-digit" maxlength="1" oninput="moveToNext(this, 3)">
                            <input type="text" class="verification-digit" maxlength="1" oninput="moveToNext(this, 4)">
                            <input type="text" class="verification-digit" maxlength="1" oninput="moveToNext(this, 5)">
                        </div>
                        <div class="text-sm text-gray-500 text-center">
                            验证码已发送至 <span id="masked-phone"></span>
                        </div>
                    </div>

                    <div class="flex gap-3 mb-4">
                        <button class="countdown-btn" id="resend-btn" onclick="resendCode()">重新发送 (60s)</button>
                        <button class="btn btn-primary flex-1" onclick="verifyCode()">验证</button>
                    </div>
                </div>

                <!-- 步骤3: 重置密码 -->
                <div id="reset-step" class="step-content" style="display: none;">
                    <div class="form-group">
                        <label class="form-label" for="new-password">新密码</label>
                        <div class="relative">
                            <span class="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500">
                                <i class="fas fa-lock"></i>
                            </span>
                            <input type="password" id="new-password" class="form-control pl-10" placeholder="请输入新密码">
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="confirm-password">确认密码</label>
                        <div class="relative">
                            <span class="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500">
                                <i class="fas fa-lock"></i>
                            </span>
                            <input type="password" id="confirm-password" class="form-control pl-10" placeholder="请再次输入新密码">
                        </div>
                    </div>

                    <div class="text-sm text-gray-500 mb-4">
                        密码长度至少8位，包含字母和数字
                    </div>

                    <button class="btn btn-primary btn-block" onclick="resetPassword()">重置密码</button>
                </div>

                <div class="text-center mt-4">
                    <a href="login.html" class="text-sm text-blue-500">返回登录</a>
                </div>
            </div>

            <div class="mt-8 text-center text-xs text-gray-500">
                © 2024 项目管理平台 版权所有
            </div>
        </div>
    </div>

    <script>
        let currentStep = 1;
        let countdown = 60;
        let countdownTimer = null;
        let userPhone = '';

        // 发送验证码
        function sendVerificationCode() {
            const phone = document.getElementById('phone').value.trim();
            
            // 验证手机号格式
            if (!phone || !/^1[3-9]\d{9}$/.test(phone)) {
                alert('请输入正确的手机号码');
                return;
            }

            userPhone = phone;
            
            // 模拟发送验证码
            alert('验证码已发送到您的手机');
            
            // 切换到验证码步骤
            showStep(2);
            
            // 显示脱敏手机号
            document.getElementById('masked-phone').textContent = 
                phone.substring(0, 3) + '****' + phone.substring(7);
            
            // 开始倒计时
            startCountdown();
        }

        // 验证验证码
        function verifyCode() {
            const digits = document.querySelectorAll('.verification-digit');
            let code = '';
            digits.forEach(digit => {
                code += digit.value;
            });

            if (code.length !== 6) {
                alert('请输入完整的验证码');
                return;
            }

            // 模拟验证码验证（这里假设正确的验证码是123456）
            if (code === '123456') {
                alert('验证码验证成功');
                showStep(3);
            } else {
                alert('验证码错误，请重新输入');
                // 清空验证码输入框
                digits.forEach(digit => {
                    digit.value = '';
                });
                digits[0].focus();
            }
        }

        // 重置密码
        function resetPassword() {
            const newPassword = document.getElementById('new-password').value;
            const confirmPassword = document.getElementById('confirm-password').value;

            if (!newPassword || newPassword.length < 8) {
                alert('密码长度至少8位');
                return;
            }

            if (!/(?=.*[a-zA-Z])(?=.*\d)/.test(newPassword)) {
                alert('密码必须包含字母和数字');
                return;
            }

            if (newPassword !== confirmPassword) {
                alert('两次输入的密码不一致');
                return;
            }

            // 模拟重置密码成功
            alert('密码重置成功，请使用新密码登录');
            window.location.href = 'login.html';
        }

        // 显示指定步骤
        function showStep(step) {
            // 隐藏所有步骤内容
            document.querySelectorAll('.step-content').forEach(content => {
                content.style.display = 'none';
            });

            // 更新步骤指示器
            for (let i = 1; i <= 3; i++) {
                const stepElement = document.getElementById(`step${i}`);
                stepElement.classList.remove('active', 'completed');
                
                if (i < step) {
                    stepElement.classList.add('completed');
                } else if (i === step) {
                    stepElement.classList.add('active');
                }
            }

            // 显示当前步骤内容
            if (step === 1) {
                document.getElementById('phone-step').style.display = 'block';
            } else if (step === 2) {
                document.getElementById('verification-step').style.display = 'block';
            } else if (step === 3) {
                document.getElementById('reset-step').style.display = 'block';
            }

            currentStep = step;
        }

        // 验证码输入框自动跳转
        function moveToNext(current, index) {
            if (current.value.length === 1 && index < 5) {
                const nextInput = document.querySelectorAll('.verification-digit')[index + 1];
                nextInput.focus();
            }
        }

        // 开始倒计时
        function startCountdown() {
            const resendBtn = document.getElementById('resend-btn');
            countdown = 60;
            resendBtn.classList.remove('active');
            resendBtn.textContent = `重新发送 (${countdown}s)`;

            countdownTimer = setInterval(() => {
                countdown--;
                resendBtn.textContent = `重新发送 (${countdown}s)`;

                if (countdown <= 0) {
                    clearInterval(countdownTimer);
                    resendBtn.classList.add('active');
                    resendBtn.textContent = '重新发送';
                }
            }, 1000);
        }

        // 重新发送验证码
        function resendCode() {
            if (countdown > 0) return;

            alert('验证码已重新发送');
            startCountdown();
        }

        // 页面加载时聚焦到手机号输入框
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('phone').focus();
        });
    </script>
</body>
</html>
