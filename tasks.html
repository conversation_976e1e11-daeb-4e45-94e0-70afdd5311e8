<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务配置 - 项目管理平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        .search-bar {
            display: flex;
            align-items: center;
            background-color: var(--light-gray);
            border-radius: 10px;
            padding: 8px 12px;
            margin-bottom: 16px;
        }

        .search-bar input {
            flex: 1;
            border: none;
            background: transparent;
            margin-left: 8px;
            font-size: 15px;
        }

        .search-bar input:focus {
            outline: none;
        }

        .segment-control {
            display: flex;
            background-color: var(--light-gray);
            border-radius: 10px;
            padding: 4px;
            margin-bottom: 20px;
        }

        .segment-item {
            flex: 1;
            text-align: center;
            padding: 8px 0;
            font-size: 14px;
            border-radius: 8px;
        }

        .segment-item.active {
            background-color: white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            font-weight: 600;
        }

        .task-card {
            background-color: white;
            border-radius: 12px;
            margin-bottom: 16px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            overflow: hidden;
        }

        .task-header {
            padding: 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid var(--light-gray);
        }

        .task-name {
            font-size: 17px;
            font-weight: 600;
        }

        .task-status {
            font-size: 13px;
            padding: 4px 10px;
            border-radius: 100px;
        }

        .task-status.running {
            background-color: rgba(52, 199, 89, 0.1);
            color: var(--success-color);
        }

        .task-status.paused {
            background-color: rgba(255, 149, 0, 0.1);
            color: var(--warning-color);
        }

        .task-status.stopped {
            background-color: rgba(142, 142, 147, 0.1);
            color: var(--dark-gray);
        }

        .task-content {
            padding: 16px;
        }

        .task-description {
            font-size: 15px;
            color: var(--dark-gray);
            margin-bottom: 12px;
        }

        .task-info {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 12px;
        }

        .task-info-item {
            display: flex;
            align-items: center;
            font-size: 13px;
            color: var(--dark-gray);
        }

        .task-info-item i {
            margin-right: 4px;
            font-size: 12px;
        }

        .task-progress {
            height: 6px;
            background-color: var(--light-gray);
            border-radius: 3px;
            margin-bottom: 12px;
            overflow: hidden;
        }

        .progress-bar {
            height: 100%;
            background-color: var(--primary-color);
        }

        .task-actions {
            display: flex;
            gap: 8px;
        }

        .task-action-btn {
            flex: 1;
            padding: 8px 0;
            border-radius: 8px;
            font-size: 13px;
            text-align: center;
            background-color: var(--light-gray);
            color: var(--dark-gray);
        }

        .task-action-btn.primary {
            background-color: rgba(0, 122, 255, 0.1);
            color: var(--primary-color);
        }

        .task-action-btn.warning {
            background-color: rgba(255, 149, 0, 0.1);
            color: var(--warning-color);
        }

        /* 任务日志样式 */
        .log-filters {
            display: flex;
            gap: 10px;
            margin-bottom: 16px;
            overflow-x: auto;
            padding-bottom: 4px;
        }

        .log-filter {
            background-color: var(--light-gray);
            border-radius: 16px;
            padding: 6px 12px;
            font-size: 13px;
            white-space: nowrap;
            color: var(--dark-gray);
        }

        .log-filter.active {
            background-color: rgba(0, 122, 255, 0.1);
            color: var(--primary-color);
        }

        .log-item {
            background-color: white;
            border-radius: 12px;
            margin-bottom: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            padding: 16px;
        }

        .log-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }

        .log-task-name {
            font-weight: 600;
            font-size: 16px;
        }

        .log-time {
            font-size: 13px;
            color: var(--dark-gray);
        }

        .log-content {
            font-size: 14px;
            color: var(--text-color);
            margin-bottom: 12px;
        }

        .log-status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .log-status.success {
            background-color: rgba(52, 199, 89, 0.1);
            color: #34C759;
        }

        .log-status.warning {
            background-color: rgba(255, 149, 0, 0.1);
            color: #FF9500;
        }

        .log-status.error {
            background-color: rgba(255, 45, 85, 0.1);
            color: #FF2D55;
        }

        .log-details {
            margin-top: 8px;
            font-size: 13px;
            color: var(--primary-color);
            display: flex;
            align-items: center;
        }

        .log-details i {
            margin-right: 4px;
        }

        /* 标签内容区域 */
        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* 浮动添加按钮样式 */
        .add-button {
            position: fixed;
            bottom: 100px;
            right: 20px;
            width: 56px;
            height: 56px;
            border-radius: 50%;
            background-color: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
            z-index: 50;
            transition: all var(--transition-fast);
        }

        .add-button:active {
            transform: scale(0.95);
            box-shadow: 0 2px 8px rgba(0, 122, 255, 0.2);
        }
    </style>
</head>
<body>
    <!-- Status Bar -->
    <div class="status-bar">
        <div>9:41</div>
        <div class="status-bar-icons">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>

    <!-- Header -->
    <div class="header">
        <div class="header-left">
            <a href="modules.html" class="text-gray-600">
                <i class="fas fa-arrow-left"></i>
            </a>
        </div>
        <div class="header-title">任务配置</div>
        <div class="header-right">
            <i class="fas fa-search text-gray-600"></i>
        </div>
    </div>

    <!-- Content -->
    <div class="content">
        <!-- Search Bar -->
        <div class="search-bar">
            <i class="fas fa-search text-gray-400"></i>
            <input type="text" placeholder="搜索任务...">
        </div>

        <!-- Segment Control -->
        <div class="segment-control">
            <div class="segment-item active" onclick="switchTab('tasks')">任务管理</div>
            <div class="segment-item" onclick="switchTab('logs')">任务日志</div>
        </div>

        <!-- 任务管理标签内容 -->
        <div id="tasks-tab" class="tab-content active">
            <!-- Tasks List -->
            <div class="task-card">
            <div class="task-header">
                <div class="task-name">数据采集任务</div>
                <div class="task-status running">运行中</div>
            </div>
            <div class="task-content">
                <div class="task-description">
                    每小时采集所有设备的运行数据并存储到数据库
                </div>
                <div class="task-info">
                    <div class="task-info-item">
                        <i class="fas fa-clock"></i>
                        <span>创建于: 2024-04-15</span>
                    </div>
                    <div class="task-info-item">
                        <i class="fas fa-calendar"></i>
                        <span>执行周期: 每小时</span>
                    </div>
                </div>
                <div class="task-progress">
                    <div class="progress-bar" style="width: 75%"></div>
                </div>
                <div class="task-actions">
                    <div class="task-action-btn warning">
                        <i class="fas fa-pause mr-1"></i> 暂停
                    </div>
                    <div class="task-action-btn primary">
                        <i class="fas fa-edit mr-1"></i> 编辑
                    </div>
                </div>
            </div>
        </div>

        <div class="task-card">
            <div class="task-header">
                <div class="task-name">设备状态检查</div>
                <div class="task-status running">运行中</div>
            </div>
            <div class="task-content">
                <div class="task-description">
                    每30分钟检查所有设备的在线状态并记录
                </div>
                <div class="task-info">
                    <div class="task-info-item">
                        <i class="fas fa-clock"></i>
                        <span>创建于: 2024-04-10</span>
                    </div>
                    <div class="task-info-item">
                        <i class="fas fa-calendar"></i>
                        <span>执行周期: 每30分钟</span>
                    </div>
                </div>
                <div class="task-progress">
                    <div class="progress-bar" style="width: 100%"></div>
                </div>
                <div class="task-actions">
                    <div class="task-action-btn warning">
                        <i class="fas fa-pause mr-1"></i> 暂停
                    </div>
                    <div class="task-action-btn primary">
                        <i class="fas fa-edit mr-1"></i> 编辑
                    </div>
                </div>
            </div>
        </div>

        <div class="task-card">
            <div class="task-header">
                <div class="task-name">数据备份任务</div>
                <div class="task-status paused">已暂停</div>
            </div>
            <div class="task-content">
                <div class="task-description">
                    每天凌晨2点备份所有系统数据
                </div>
                <div class="task-info">
                    <div class="task-info-item">
                        <i class="fas fa-clock"></i>
                        <span>创建于: 2024-04-05</span>
                    </div>
                    <div class="task-info-item">
                        <i class="fas fa-calendar"></i>
                        <span>执行周期: 每天</span>
                    </div>
                </div>
                <div class="task-progress">
                    <div class="progress-bar" style="width: 0%"></div>
                </div>
                <div class="task-actions">
                    <div class="task-action-btn">
                        <i class="fas fa-play mr-1"></i> 启动
                    </div>
                    <div class="task-action-btn primary">
                        <i class="fas fa-edit mr-1"></i> 编辑
                    </div>
                </div>
            </div>
        </div>

        <div class="task-card">
            <div class="task-header">
                <div class="task-name">系统性能监控</div>
                <div class="task-status stopped">已停止</div>
            </div>
            <div class="task-content">
                <div class="task-description">
                    监控系统性能指标并生成报告
                </div>
                <div class="task-info">
                    <div class="task-info-item">
                        <i class="fas fa-clock"></i>
                        <span>创建于: 2024-03-28</span>
                    </div>
                    <div class="task-info-item">
                        <i class="fas fa-calendar"></i>
                        <span>执行周期: 每周</span>
                    </div>
                </div>
                <div class="task-progress">
                    <div class="progress-bar" style="width: 0%"></div>
                </div>
                <div class="task-actions">
                    <div class="task-action-btn">
                        <i class="fas fa-play mr-1"></i> 启动
                    </div>
                    <div class="task-action-btn primary">
                        <i class="fas fa-edit mr-1"></i> 编辑
                    </div>
                </div>
            </div>
        </div>
        </div>

        <!-- 任务日志标签内容 -->
        <div id="logs-tab" class="tab-content">
            <!-- 日志筛选器 -->
            <div class="log-filters">
                <div class="log-filter active">全部</div>
                <div class="log-filter">今天</div>
                <div class="log-filter">昨天</div>
                <div class="log-filter">本周</div>
                <div class="log-filter">成功</div>
                <div class="log-filter">警告</div>
                <div class="log-filter">错误</div>
            </div>

            <!-- 日志列表 -->
            <div class="log-item">
                <div class="log-header">
                    <div class="log-task-name">数据采集任务</div>
                    <div class="log-time">今天 15:00</div>
                </div>
                <div class="log-content">
                    成功采集了128台设备的运行数据，数据已存储到数据库
                </div>
                <div class="log-status success">执行成功</div>
                <div class="log-details">
                    <i class="fas fa-chevron-right"></i> 查看详情
                </div>
            </div>

            <div class="log-item">
                <div class="log-header">
                    <div class="log-task-name">设备状态检查</div>
                    <div class="log-time">今天 14:30</div>
                </div>
                <div class="log-content">
                    检测到3台设备离线，已发送告警通知
                </div>
                <div class="log-status warning">需要关注</div>
                <div class="log-details">
                    <i class="fas fa-chevron-right"></i> 查看详情
                </div>
            </div>

            <div class="log-item">
                <div class="log-header">
                    <div class="log-task-name">数据采集任务</div>
                    <div class="log-time">今天 14:00</div>
                </div>
                <div class="log-content">
                    成功采集了130台设备的运行数据，数据已存储到数据库
                </div>
                <div class="log-status success">执行成功</div>
                <div class="log-details">
                    <i class="fas fa-chevron-right"></i> 查看详情
                </div>
            </div>

            <div class="log-item">
                <div class="log-header">
                    <div class="log-task-name">数据备份任务</div>
                    <div class="log-time">昨天 02:00</div>
                </div>
                <div class="log-content">
                    备份过程中断，存储空间不足
                </div>
                <div class="log-status error">执行失败</div>
                <div class="log-details">
                    <i class="fas fa-chevron-right"></i> 查看详情
                </div>
            </div>

            <div class="log-item">
                <div class="log-header">
                    <div class="log-task-name">设备状态检查</div>
                    <div class="log-time">昨天 00:30</div>
                </div>
                <div class="log-content">
                    所有设备状态正常
                </div>
                <div class="log-status success">执行成功</div>
                <div class="log-details">
                    <i class="fas fa-chevron-right"></i> 查看详情
                </div>
            </div>
        </div>
    </div>

    <!-- 添加按钮 -->
    <a href="#" class="add-button animate-pulse">
        <i class="fas fa-plus"></i>
    </a>

    <!-- Navigation Bar -->
    <div class="nav-bar">
        <a href="home.html" class="nav-item">
            <i class="nav-icon fas fa-home"></i>
            <span>首页</span>
        </a>
        <a href="modules.html" class="nav-item active">
            <i class="nav-icon fas fa-th-large"></i>
            <span>功能</span>
        </a>
        <a href="users.html" class="nav-item">
            <i class="nav-icon fas fa-cog"></i>
            <span>系统</span>
        </a>
        <a href="profile.html" class="nav-item">
            <i class="nav-icon fas fa-user"></i>
            <span>我的</span>
        </a>
    </div>

    <script>
        function switchTab(tabName) {
            // 隐藏所有内容
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            // 取消所有标签的选中状态
            document.querySelectorAll('.segment-item').forEach(item => {
                item.classList.remove('active');
            });

            // 显示选中的内容
            document.getElementById(tabName + '-tab').classList.add('active');

            // 设置选中标签的样式
            event.currentTarget.classList.add('active');
        }
    </script>
</body>
</html>
