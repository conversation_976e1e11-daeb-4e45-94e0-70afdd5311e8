<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的 - 集成商登陆平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        .profile-header {
            padding: 24px 16px;
            display: flex;
            align-items: center;
            background-color: white;
            margin-bottom: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }
        
        .profile-avatar {
            width: 80px;
            height: 80px;
            border-radius: 40px;
            background-color: var(--light-gray);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            overflow: hidden;
        }
        
        .profile-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .profile-info {
            flex: 1;
        }
        
        .profile-name {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .profile-role {
            font-size: 15px;
            color: var(--dark-gray);
            margin-bottom: 8px;
        }
        
        .profile-company {
            font-size: 14px;
            color: var(--primary-color);
            display: flex;
            align-items: center;
        }
        
        .profile-company i {
            margin-right: 4px;
        }
        
        .menu-section {
            margin-bottom: 24px;
        }
        
        .section-title {
            font-size: 17px;
            font-weight: 600;
            margin-bottom: 12px;
            padding-left: 16px;
        }
        
        .menu-item {
            display: flex;
            align-items: center;
            padding: 16px;
            background-color: white;
            border-bottom: 1px solid var(--light-gray);
        }
        
        .menu-item:first-child {
            border-top-left-radius: 12px;
            border-top-right-radius: 12px;
        }
        
        .menu-item:last-child {
            border-bottom-left-radius: 12px;
            border-bottom-right-radius: 12px;
            border-bottom: none;
        }
        
        .menu-icon {
            width: 36px;
            height: 36px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 18px;
        }
        
        .menu-icon.blue {
            background-color: rgba(0, 122, 255, 0.1);
            color: var(--primary-color);
        }
        
        .menu-icon.green {
            background-color: rgba(52, 199, 89, 0.1);
            color: var(--success-color);
        }
        
        .menu-icon.orange {
            background-color: rgba(255, 149, 0, 0.1);
            color: var(--warning-color);
        }
        
        .menu-icon.red {
            background-color: rgba(255, 59, 48, 0.1);
            color: var(--danger-color);
        }
        
        .menu-icon.purple {
            background-color: rgba(175, 82, 222, 0.1);
            color: #af52de;
        }
        
        .menu-icon.teal {
            background-color: rgba(90, 200, 250, 0.1);
            color: var(--secondary-color);
        }
        
        .menu-text {
            flex: 1;
            font-size: 17px;
        }
        
        .menu-arrow {
            color: var(--medium-gray);
        }
        
        .version-info {
            text-align: center;
            font-size: 13px;
            color: var(--dark-gray);
            margin-top: 40px;
        }
    </style>
</head>
<body>
    <!-- Status Bar -->
    <div class="status-bar">
        <div>9:41</div>
        <div class="status-bar-icons">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>

    <!-- Header -->
    <div class="header">
        <div class="header-title">我的</div>
        <div class="header-right">
            <i class="fas fa-cog text-gray-600"></i>
        </div>
    </div>

    <!-- Content -->
    <div class="content">
        <!-- Profile Header -->
        <div class="profile-header">
            <div class="profile-avatar">
                <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="Profile Avatar">
            </div>
            <div class="profile-info">
                <div class="profile-name">张三</div>
                <div class="profile-role">系统管理员</div>
                <div class="profile-company">
                    <i class="fas fa-building"></i>
                    <span>智慧科技有限公司</span>
                </div>
            </div>
        </div>
        
        <!-- Account Section -->
        <div class="menu-section">
            <div class="section-title">账户管理</div>
            <div class="card">
                <div class="menu-item">
                    <div class="menu-icon blue">
                        <i class="fas fa-user-circle"></i>
                    </div>
                    <div class="menu-text">个人信息</div>
                    <div class="menu-arrow">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </div>
                <div class="menu-item">
                    <div class="menu-icon green">
                        <i class="fas fa-key"></i>
                    </div>
                    <div class="menu-text">修改密码</div>
                    <div class="menu-arrow">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </div>
                <div class="menu-item">
                    <div class="menu-icon orange">
                        <i class="fas fa-bell"></i>
                    </div>
                    <div class="menu-text">消息通知</div>
                    <div class="menu-arrow">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Projects Section -->
        <div class="menu-section">
            <div class="section-title">我的项目</div>
            <div class="card">
                <div class="menu-item">
                    <div class="menu-icon purple">
                        <i class="fas fa-project-diagram"></i>
                    </div>
                    <div class="menu-text">项目列表</div>
                    <div class="menu-arrow">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </div>
                <div class="menu-item">
                    <div class="menu-icon teal">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="menu-text">数据统计</div>
                    <div class="menu-arrow">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </div>
                <div class="menu-item">
                    <div class="menu-icon blue">
                        <i class="fas fa-history"></i>
                    </div>
                    <div class="menu-text">操作记录</div>
                    <div class="menu-arrow">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Support Section -->
        <div class="menu-section">
            <div class="section-title">支持与帮助</div>
            <div class="card">
                <div class="menu-item">
                    <div class="menu-icon green">
                        <i class="fas fa-question-circle"></i>
                    </div>
                    <div class="menu-text">帮助中心</div>
                    <div class="menu-arrow">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </div>
                <div class="menu-item">
                    <div class="menu-icon blue">
                        <i class="fas fa-headset"></i>
                    </div>
                    <div class="menu-text">联系客服</div>
                    <div class="menu-arrow">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </div>
                <div class="menu-item">
                    <div class="menu-icon red">
                        <i class="fas fa-sign-out-alt"></i>
                    </div>
                    <div class="menu-text">退出登录</div>
                    <div class="menu-arrow">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="version-info">
            版本 1.0.0 (Build 2024050601)
        </div>
    </div>

    <!-- Navigation Bar -->
    <div class="nav-bar">
        <a href="home.html" class="nav-item">
            <i class="nav-icon fas fa-home"></i>
            <span>首页</span>
        </a>
        <a href="gateway.html" class="nav-item">
            <i class="nav-icon fas fa-th-large"></i>
            <span>功能</span>
        </a>
        <a href="users.html" class="nav-item">
            <i class="nav-icon fas fa-cog"></i>
            <span>系统</span>
        </a>
        <a href="profile.html" class="nav-item active">
            <i class="nav-icon fas fa-user"></i>
            <span>我的</span>
        </a>
    </div>
</body>
</html>
