<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网关管理 - 项目管理平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        .search-bar {
            display: flex;
            align-items: center;
            background-color: var(--light-gray);
            border-radius: 10px;
            padding: 8px 12px;
            margin-bottom: 16px;
        }

        .search-bar input {
            flex: 1;
            border: none;
            background: transparent;
            margin-left: 8px;
            font-size: 15px;
        }

        .search-bar input:focus {
            outline: none;
        }

        .gateway-card {
            display: flex;
            align-items: center;
            padding: 18px;
            border-radius: 16px;
            background-color: white;
            box-shadow: var(--shadow-md);
            margin-bottom: 18px;
            border: 1px solid rgba(0, 0, 0, 0.03);
            transition: all var(--transition-fast);
        }

        .gateway-card:active {
            transform: translateY(2px);
            box-shadow: var(--shadow-sm);
        }

        .gateway-icon {
            width: 64px;
            height: 64px;
            background-color: rgba(0, 122, 255, 0.1);
            border-radius: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-color);
            font-size: 24px;
            margin-right: 18px;
            box-shadow: 0 4px 12px rgba(0, 122, 255, 0.15);
        }

        .gateway-info {
            flex: 1;
        }

        .gateway-name {
            font-size: 17px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .gateway-details {
            display: flex;
            gap: 12px;
            font-size: 13px;
            color: var(--dark-gray);
        }

        .gateway-status {
            display: flex;
            align-items: center;
            font-size: 13px;
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 4px;
        }

        .status-online {
            background-color: var(--success-color);
        }

        .status-offline {
            background-color: var(--danger-color);
        }

        .status-warning {
            background-color: var(--warning-color);
        }

        .filter-tabs {
            display: flex;
            overflow-x: auto;
            margin-bottom: 16px;
            -webkit-overflow-scrolling: touch;
        }

        .filter-tab {
            padding: 8px 16px;
            border-radius: 100px;
            font-size: 14px;
            white-space: nowrap;
            margin-right: 8px;
        }

        .filter-tab.active {
            background-color: var(--primary-color);
            color: white;
        }

        .filter-tab:not(.active) {
            background-color: var(--light-gray);
            color: var(--dark-gray);
        }

        .action-button {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: var(--light-gray);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--dark-gray);
            transition: all var(--transition-fast);
            cursor: pointer;
        }

        .action-button:active {
            transform: scale(0.9);
            background-color: rgba(0, 0, 0, 0.05);
        }

        .add-button {
            position: fixed;
            bottom: 100px;
            right: 20px;
            width: 56px;
            height: 56px;
            border-radius: 50%;
            background-color: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
            z-index: 50;
            transition: all var(--transition-fast);
        }

        .add-button:active {
            transform: scale(0.95);
            box-shadow: 0 2px 8px rgba(0, 122, 255, 0.2);
        }
    </style>
</head>
<body>
    <!-- Status Bar -->
    <div class="status-bar">
        <div>9:41</div>
        <div class="status-bar-icons">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>

    <!-- Header -->
    <div class="header">
        <div class="header-left">
            <a href="modules.html" class="text-gray-600">
                <i class="fas fa-arrow-left"></i>
            </a>
        </div>
        <div class="header-title">网关管理</div>
        <div class="header-right">
            <i class="fas fa-search text-gray-600"></i>
        </div>
    </div>

    <!-- Content -->
    <div class="content">
        <!-- Search Bar -->
        <div class="search-bar">
            <i class="fas fa-search text-gray-400"></i>
            <input type="text" placeholder="搜索网关...">
        </div>

        <!-- Filter Tabs -->
        <div class="filter-tabs">
            <div class="filter-tab active">全部</div>
            <div class="filter-tab">在线</div>
            <div class="filter-tab">离线</div>
            <div class="filter-tab">告警</div>
            <div class="filter-tab">维护中</div>
        </div>

        <!-- Gateway List -->
        <div class="gateway-card">
            <div class="gateway-icon">
                <i class="fas fa-network-wired"></i>
            </div>
            <div class="gateway-info">
                <div class="gateway-name">GW-001 主楼网关</div>
                <div class="gateway-details">
                    <span>ID: 10086</span>
                    <span>IP: *************</span>
                </div>
                <div class="gateway-status mt-2">
                    <div class="status-indicator status-online"></div>
                    <span class="text-green-500">在线</span>
                    <span class="ml-2 text-gray-500">· 最后心跳: 1分钟前</span>
                </div>
            </div>
            <div class="action-button">
                <i class="fas fa-ellipsis-v"></i>
            </div>
        </div>

        <div class="gateway-card">
            <div class="gateway-icon">
                <i class="fas fa-network-wired"></i>
            </div>
            <div class="gateway-info">
                <div class="gateway-name">GW-002 东区网关</div>
                <div class="gateway-details">
                    <span>ID: 10087</span>
                    <span>IP: *************</span>
                </div>
                <div class="gateway-status mt-2">
                    <div class="status-indicator status-warning"></div>
                    <span class="text-yellow-500">告警</span>
                    <span class="ml-2 text-gray-500">· 2个告警</span>
                </div>
            </div>
            <div class="action-button">
                <i class="fas fa-ellipsis-v"></i>
            </div>
        </div>

        <div class="gateway-card">
            <div class="gateway-icon">
                <i class="fas fa-network-wired"></i>
            </div>
            <div class="gateway-info">
                <div class="gateway-name">GW-003 西区网关</div>
                <div class="gateway-details">
                    <span>ID: 10088</span>
                    <span>IP: *************</span>
                </div>
                <div class="gateway-status mt-2">
                    <div class="status-indicator status-offline"></div>
                    <span class="text-red-500">离线</span>
                    <span class="ml-2 text-gray-500">· 最后心跳: 2小时前</span>
                </div>
            </div>
            <div class="action-button">
                <i class="fas fa-ellipsis-v"></i>
            </div>
        </div>

        <div class="gateway-card">
            <div class="gateway-icon">
                <i class="fas fa-network-wired"></i>
            </div>
            <div class="gateway-info">
                <div class="gateway-name">GW-004 南区网关</div>
                <div class="gateway-details">
                    <span>ID: 10089</span>
                    <span>IP: *************</span>
                </div>
                <div class="gateway-status mt-2">
                    <div class="status-indicator status-online"></div>
                    <span class="text-green-500">在线</span>
                    <span class="ml-2 text-gray-500">· 最后心跳: 5分钟前</span>
                </div>
            </div>
            <div class="action-button">
                <i class="fas fa-ellipsis-v"></i>
            </div>
        </div>
    </div>

    <!-- Add Button -->
    <a href="gateway-add.html" class="add-button animate-pulse">
        <i class="fas fa-plus"></i>
    </a>

    <!-- Navigation Bar -->
    <div class="nav-bar">
        <a href="home.html" class="nav-item">
            <i class="nav-icon fas fa-home"></i>
            <span>首页</span>
        </a>
        <a href="modules.html" class="nav-item active">
            <i class="nav-icon fas fa-th-large"></i>
            <span>功能</span>
        </a>
        <a href="users.html" class="nav-item">
            <i class="nav-icon fas fa-cog"></i>
            <span>系统</span>
        </a>
        <a href="profile.html" class="nav-item">
            <i class="nav-icon fas fa-user"></i>
            <span>我的</span>
        </a>
    </div>
</body>
</html>
