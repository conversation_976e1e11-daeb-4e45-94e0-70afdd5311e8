<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>空间配置 - 项目管理平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        .search-bar {
            display: flex;
            align-items: center;
            background-color: var(--light-gray);
            border-radius: 10px;
            padding: 8px 12px;
            margin-bottom: 16px;
        }

        .search-bar input {
            flex: 1;
            border: none;
            background: transparent;
            margin-left: 8px;
            font-size: 15px;
        }

        .search-bar input:focus {
            outline: none;
        }

        .segment-control {
            display: flex;
            background-color: var(--light-gray);
            border-radius: 10px;
            padding: 4px;
            margin-bottom: 20px;
        }

        .segment-item {
            flex: 1;
            text-align: center;
            padding: 8px 0;
            font-size: 14px;
            border-radius: 8px;
        }

        .segment-item.active {
            background-color: white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            font-weight: 600;
        }

        .space-card {
            background-color: white;
            border-radius: 12px;
            margin-bottom: 16px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            overflow: hidden;
        }

        .space-header {
            height: 120px;
            background-size: cover;
            background-position: center;
            position: relative;
        }

        .space-header-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 16px;
            background: linear-gradient(to top, rgba(0,0,0,0.7), transparent);
            color: white;
        }

        .space-name {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .space-type {
            font-size: 14px;
            opacity: 0.8;
        }

        .space-content {
            padding: 16px;
        }

        .space-stats {
            display: flex;
            justify-content: space-between;
            margin-bottom: 16px;
        }

        .space-stat {
            text-align: center;
        }

        .stat-value {
            font-size: 18px;
            font-weight: 600;
            color: var(--primary-color);
        }

        .stat-label {
            font-size: 12px;
            color: var(--dark-gray);
        }

        .space-actions {
            display: flex;
            gap: 8px;
        }

        .space-action-btn {
            flex: 1;
            padding: 8px 0;
            border-radius: 8px;
            font-size: 13px;
            text-align: center;
            background-color: var(--light-gray);
            color: var(--dark-gray);
        }

        .space-action-btn.primary {
            background-color: rgba(0, 122, 255, 0.1);
            color: var(--primary-color);
        }

        .space-tree {
            margin-top: 24px;
        }

        .tree-title {
            font-size: 17px;
            font-weight: 600;
            margin-bottom: 12px;
        }

        .tree-item {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            border-bottom: 1px solid var(--light-gray);
        }

        .tree-item:last-child {
            border-bottom: none;
        }

        .tree-icon {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            background-color: rgba(0, 122, 255, 0.1);
            color: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
        }

        .tree-name {
            flex: 1;
            font-size: 15px;
        }

        .tree-arrow {
            color: var(--medium-gray);
        }

        .tree-level-1 {
            padding-left: 16px;
        }

        .tree-level-2 {
            padding-left: 32px;
        }

        /* 空间类型样式 */
        .type-card {
            background-color: white;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            transition: all var(--transition-fast);
        }

        .type-card:active {
            transform: translateY(2px);
            box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
        }

        .type-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            margin-right: 16px;
            flex-shrink: 0;
        }

        .type-info {
            flex: 1;
        }

        .type-name {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .type-count {
            font-size: 13px;
            color: var(--dark-gray);
        }

        .type-actions {
            display: flex;
            gap: 8px;
        }

        .action-icon {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background-color: var(--light-gray);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--dark-gray);
            font-size: 14px;
            transition: all var(--transition-fast);
        }

        .action-icon:active {
            transform: scale(0.9);
            background-color: rgba(0, 0, 0, 0.05);
        }

        /* 标签内容区域 */
        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* 浮动添加按钮样式 */
        .add-button {
            position: fixed;
            bottom: 100px;
            right: 20px;
            width: 56px;
            height: 56px;
            border-radius: 50%;
            background-color: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
            z-index: 50;
            transition: all var(--transition-fast);
        }

        .add-button:active {
            transform: scale(0.95);
            box-shadow: 0 2px 8px rgba(0, 122, 255, 0.2);
        }
    </style>
</head>
<body>
    <!-- Status Bar -->
    <div class="status-bar">
        <div>9:41</div>
        <div class="status-bar-icons">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>

    <!-- Header -->
    <div class="header">
        <div class="header-left">
            <a href="modules.html" class="text-gray-600">
                <i class="fas fa-arrow-left"></i>
            </a>
        </div>
        <div class="header-title">空间配置</div>
        <div class="header-right">
            <i class="fas fa-search text-gray-600"></i>
        </div>
    </div>

    <!-- Content -->
    <div class="content">
        <!-- Search Bar -->
        <div class="search-bar">
            <i class="fas fa-search text-gray-400"></i>
            <input type="text" placeholder="搜索空间...">
        </div>

        <!-- Segment Control -->
        <div class="segment-control">
            <div class="segment-item active" onclick="switchTab('spaces')">空间管理</div>
            <div class="segment-item" onclick="switchTab('types')">空间类型</div>
        </div>

        <!-- 空间管理标签内容 -->
        <div id="spaces-tab" class="tab-content active">
            <!-- Space Card -->
        <div class="space-card">
            <div class="space-header" style="background-image: url('https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80')">
                <div class="space-header-overlay">
                    <div class="space-name">智慧园区</div>
                    <div class="space-type">商业园区</div>
                </div>
            </div>
            <div class="space-content">
                <div class="space-stats">
                    <div class="space-stat">
                        <div class="stat-value">5</div>
                        <div class="stat-label">建筑</div>
                    </div>
                    <div class="space-stat">
                        <div class="stat-value">25</div>
                        <div class="stat-label">楼层</div>
                    </div>
                    <div class="space-stat">
                        <div class="stat-value">120</div>
                        <div class="stat-label">房间</div>
                    </div>
                    <div class="space-stat">
                        <div class="stat-value">86</div>
                        <div class="stat-label">设备</div>
                    </div>
                </div>
                <div class="space-actions">
                    <div class="space-action-btn">
                        <i class="fas fa-map mr-1"></i> 查看地图
                    </div>
                    <div class="space-action-btn primary">
                        <i class="fas fa-edit mr-1"></i> 编辑空间
                    </div>
                </div>
            </div>
        </div>

        <!-- Space Tree -->
        <div class="space-tree">
            <div class="tree-title">空间层级</div>
            <div class="card">
                <div class="tree-item">
                    <div class="tree-icon">
                        <i class="fas fa-building"></i>
                    </div>
                    <div class="tree-name">智慧园区</div>
                    <div class="tree-arrow">
                        <i class="fas fa-chevron-down"></i>
                    </div>
                </div>

                <div class="tree-item tree-level-1">
                    <div class="tree-icon">
                        <i class="fas fa-building"></i>
                    </div>
                    <div class="tree-name">A栋办公楼</div>
                    <div class="tree-arrow">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </div>

                <div class="tree-item tree-level-1">
                    <div class="tree-icon">
                        <i class="fas fa-building"></i>
                    </div>
                    <div class="tree-name">B栋办公楼</div>
                    <div class="tree-arrow">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </div>

                <div class="tree-item tree-level-1">
                    <div class="tree-icon">
                        <i class="fas fa-building"></i>
                    </div>
                    <div class="tree-name">C栋研发中心</div>
                    <div class="tree-arrow">
                        <i class="fas fa-chevron-down"></i>
                    </div>
                </div>

                <div class="tree-item tree-level-2">
                    <div class="tree-icon">
                        <i class="fas fa-layer-group"></i>
                    </div>
                    <div class="tree-name">1楼</div>
                    <div class="tree-arrow">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </div>

                <div class="tree-item tree-level-2">
                    <div class="tree-icon">
                        <i class="fas fa-layer-group"></i>
                    </div>
                    <div class="tree-name">2楼</div>
                    <div class="tree-arrow">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </div>

                <div class="tree-item tree-level-1">
                    <div class="tree-icon">
                        <i class="fas fa-building"></i>
                    </div>
                    <div class="tree-name">D栋会议中心</div>
                    <div class="tree-arrow">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </div>

                <div class="tree-item tree-level-1">
                    <div class="tree-icon">
                        <i class="fas fa-building"></i>
                    </div>
                    <div class="tree-name">E栋数据中心</div>
                    <div class="tree-arrow">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </div>
            </div>
        </div>
        </div>

        <!-- 空间类型标签内容 -->
        <div id="types-tab" class="tab-content">
            <div class="type-card">
                <div class="type-icon" style="background-color: #FF9500;">
                    <i class="fas fa-building"></i>
                </div>
                <div class="type-info">
                    <div class="type-name">商业园区</div>
                    <div class="type-count">3 个空间</div>
                </div>
                <div class="type-actions">
                    <div class="action-icon">
                        <i class="fas fa-edit"></i>
                    </div>
                    <div class="action-icon">
                        <i class="fas fa-ellipsis-v"></i>
                    </div>
                </div>
            </div>

            <div class="type-card">
                <div class="type-icon" style="background-color: #34C759;">
                    <i class="fas fa-industry"></i>
                </div>
                <div class="type-info">
                    <div class="type-name">工业园区</div>
                    <div class="type-count">2 个空间</div>
                </div>
                <div class="type-actions">
                    <div class="action-icon">
                        <i class="fas fa-edit"></i>
                    </div>
                    <div class="action-icon">
                        <i class="fas fa-ellipsis-v"></i>
                    </div>
                </div>
            </div>

            <div class="type-card">
                <div class="type-icon" style="background-color: #5856D6;">
                    <i class="fas fa-school"></i>
                </div>
                <div class="type-info">
                    <div class="type-name">教育园区</div>
                    <div class="type-count">1 个空间</div>
                </div>
                <div class="type-actions">
                    <div class="action-icon">
                        <i class="fas fa-edit"></i>
                    </div>
                    <div class="action-icon">
                        <i class="fas fa-ellipsis-v"></i>
                    </div>
                </div>
            </div>

            <div class="type-card">
                <div class="type-icon" style="background-color: #FF2D55;">
                    <i class="fas fa-hospital"></i>
                </div>
                <div class="type-info">
                    <div class="type-name">医疗园区</div>
                    <div class="type-count">1 个空间</div>
                </div>
                <div class="type-actions">
                    <div class="action-icon">
                        <i class="fas fa-edit"></i>
                    </div>
                    <div class="action-icon">
                        <i class="fas fa-ellipsis-v"></i>
                    </div>
                </div>
            </div>

            <div class="type-card">
                <div class="type-icon" style="background-color: #007AFF;">
                    <i class="fas fa-home"></i>
                </div>
                <div class="type-info">
                    <div class="type-name">住宅小区</div>
                    <div class="type-count">2 个空间</div>
                </div>
                <div class="type-actions">
                    <div class="action-icon">
                        <i class="fas fa-edit"></i>
                    </div>
                    <div class="action-icon">
                        <i class="fas fa-ellipsis-v"></i>
                    </div>
                </div>
            </div>

            <div class="type-card">
                <div class="type-icon" style="background-color: #FF9500;">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <div class="type-info">
                    <div class="type-name">商业中心</div>
                    <div class="type-count">1 个空间</div>
                </div>
                <div class="type-actions">
                    <div class="action-icon">
                        <i class="fas fa-edit"></i>
                    </div>
                    <div class="action-icon">
                        <i class="fas fa-ellipsis-v"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加按钮 -->
    <a href="#" class="add-button animate-pulse">
        <i class="fas fa-plus"></i>
    </a>

    <!-- Navigation Bar -->
    <div class="nav-bar">
        <a href="home.html" class="nav-item">
            <i class="nav-icon fas fa-home"></i>
            <span>首页</span>
        </a>
        <a href="modules.html" class="nav-item active">
            <i class="nav-icon fas fa-th-large"></i>
            <span>功能</span>
        </a>
        <a href="users.html" class="nav-item">
            <i class="nav-icon fas fa-cog"></i>
            <span>系统</span>
        </a>
        <a href="profile.html" class="nav-item">
            <i class="nav-icon fas fa-user"></i>
            <span>我的</span>
        </a>
    </div>

    <script>
        function switchTab(tabName) {
            // 隐藏所有内容
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            // 取消所有标签的选中状态
            document.querySelectorAll('.segment-item').forEach(item => {
                item.classList.remove('active');
            });

            // 显示选中的内容
            document.getElementById(tabName + '-tab').classList.add('active');

            // 设置选中标签的样式
            event.currentTarget.classList.add('active');
        }
    </script>
</body>
</html>
