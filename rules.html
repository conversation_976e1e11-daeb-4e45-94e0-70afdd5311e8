<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>规则配置 - 项目管理平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        .search-bar {
            display: flex;
            align-items: center;
            background-color: var(--light-gray);
            border-radius: 10px;
            padding: 8px 12px;
            margin-bottom: 16px;
        }

        .search-bar input {
            flex: 1;
            border: none;
            background: transparent;
            margin-left: 8px;
            font-size: 15px;
        }

        .search-bar input:focus {
            outline: none;
        }

        .segment-control {
            display: flex;
            background-color: var(--light-gray);
            border-radius: 10px;
            padding: 4px;
            margin-bottom: 20px;
        }

        .segment-item {
            flex: 1;
            text-align: center;
            padding: 8px 0;
            font-size: 14px;
            border-radius: 8px;
        }

        .segment-item.active {
            background-color: white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            font-weight: 600;
        }

        .rule-card {
            background-color: white;
            border-radius: 12px;
            margin-bottom: 16px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            overflow: hidden;
        }

        .rule-header {
            padding: 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid var(--light-gray);
        }

        .rule-name {
            font-size: 17px;
            font-weight: 600;
        }

        .rule-toggle {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 28px;
        }

        .rule-toggle input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: var(--medium-gray);
            transition: .4s;
            border-radius: 34px;
        }

        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 24px;
            width: 24px;
            left: 2px;
            bottom: 2px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .toggle-slider {
            background-color: var(--primary-color);
        }

        input:checked + .toggle-slider:before {
            transform: translateX(22px);
        }

        .rule-content {
            padding: 16px;
        }

        .rule-description {
            font-size: 15px;
            color: var(--dark-gray);
            margin-bottom: 12px;
        }

        .rule-info {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 12px;
        }

        .rule-info-item {
            display: flex;
            align-items: center;
            font-size: 13px;
            color: var(--dark-gray);
        }

        .rule-info-item i {
            margin-right: 4px;
            font-size: 12px;
        }

        .rule-actions {
            display: flex;
            gap: 8px;
        }

        .rule-action-btn {
            flex: 1;
            padding: 8px 0;
            border-radius: 8px;
            font-size: 13px;
            text-align: center;
            background-color: var(--light-gray);
            color: var(--dark-gray);
        }

        .rule-action-btn.primary {
            background-color: rgba(0, 122, 255, 0.1);
            color: var(--primary-color);
        }

        /* 规则日志样式 */
        .log-filters {
            display: flex;
            gap: 10px;
            margin-bottom: 16px;
            overflow-x: auto;
            padding-bottom: 4px;
        }

        .log-filter {
            background-color: var(--light-gray);
            border-radius: 16px;
            padding: 6px 12px;
            font-size: 13px;
            white-space: nowrap;
            color: var(--dark-gray);
        }

        .log-filter.active {
            background-color: rgba(0, 122, 255, 0.1);
            color: var(--primary-color);
        }

        .log-item {
            background-color: white;
            border-radius: 12px;
            margin-bottom: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            padding: 16px;
        }

        .log-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }

        .log-rule-name {
            font-weight: 600;
            font-size: 16px;
        }

        .log-time {
            font-size: 13px;
            color: var(--dark-gray);
        }

        .log-content {
            font-size: 14px;
            color: var(--text-color);
            margin-bottom: 12px;
        }

        .log-status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .log-status.success {
            background-color: rgba(52, 199, 89, 0.1);
            color: #34C759;
        }

        .log-status.warning {
            background-color: rgba(255, 149, 0, 0.1);
            color: #FF9500;
        }

        .log-status.error {
            background-color: rgba(255, 45, 85, 0.1);
            color: #FF2D55;
        }

        .log-details {
            margin-top: 8px;
            font-size: 13px;
            color: var(--primary-color);
            display: flex;
            align-items: center;
        }

        .log-details i {
            margin-right: 4px;
        }

        /* 标签内容区域 */
        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <!-- Status Bar -->
    <div class="status-bar">
        <div>9:41</div>
        <div class="status-bar-icons">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>

    <!-- Header -->
    <div class="header">
        <div class="header-left">
            <a href="modules.html" class="text-gray-600">
                <i class="fas fa-arrow-left"></i>
            </a>
        </div>
        <div class="header-title">规则配置</div>
        <div class="header-right">
            <i class="fas fa-search text-gray-600"></i>
        </div>
    </div>

    <!-- Content -->
    <div class="content">
        <!-- Search Bar -->
        <div class="search-bar">
            <i class="fas fa-search text-gray-400"></i>
            <input type="text" placeholder="搜索规则...">
        </div>

        <!-- Segment Control -->
        <div class="segment-control">
            <div class="segment-item active" onclick="switchTab('rules')">规则管理</div>
            <div class="segment-item" onclick="switchTab('logs')">规则日志</div>
        </div>

        <!-- 规则管理标签内容 -->
        <div id="rules-tab" class="tab-content active">
            <!-- Rules List -->
            <div class="rule-card">
            <div class="rule-header">
                <div class="rule-name">温度异常告警</div>
                <label class="rule-toggle">
                    <input type="checkbox" checked>
                    <span class="toggle-slider"></span>
                </label>
            </div>
            <div class="rule-content">
                <div class="rule-description">
                    当温度传感器检测到温度超过设定阈值时触发告警
                </div>
                <div class="rule-info">
                    <div class="rule-info-item">
                        <i class="fas fa-clock"></i>
                        <span>创建于: 2024-04-15</span>
                    </div>
                    <div class="rule-info-item">
                        <i class="fas fa-bolt"></i>
                        <span>触发: 12次</span>
                    </div>
                </div>
                <div class="rule-actions">
                    <div class="rule-action-btn">
                        <i class="fas fa-history mr-1"></i> 历史记录
                    </div>
                    <div class="rule-action-btn primary">
                        <i class="fas fa-edit mr-1"></i> 编辑规则
                    </div>
                </div>
            </div>
        </div>

        <div class="rule-card">
            <div class="rule-header">
                <div class="rule-name">人流量统计</div>
                <label class="rule-toggle">
                    <input type="checkbox" checked>
                    <span class="toggle-slider"></span>
                </label>
            </div>
            <div class="rule-content">
                <div class="rule-description">
                    每小时统计园区各区域人流量并生成报表
                </div>
                <div class="rule-info">
                    <div class="rule-info-item">
                        <i class="fas fa-clock"></i>
                        <span>创建于: 2024-04-10</span>
                    </div>
                    <div class="rule-info-item">
                        <i class="fas fa-bolt"></i>
                        <span>触发: 168次</span>
                    </div>
                </div>
                <div class="rule-actions">
                    <div class="rule-action-btn">
                        <i class="fas fa-history mr-1"></i> 历史记录
                    </div>
                    <div class="rule-action-btn primary">
                        <i class="fas fa-edit mr-1"></i> 编辑规则
                    </div>
                </div>
            </div>
        </div>

        <div class="rule-card">
            <div class="rule-header">
                <div class="rule-name">能耗异常检测</div>
                <label class="rule-toggle">
                    <input type="checkbox">
                    <span class="toggle-slider"></span>
                </label>
            </div>
            <div class="rule-content">
                <div class="rule-description">
                    监测设备能耗，当出现异常波动时发送通知
                </div>
                <div class="rule-info">
                    <div class="rule-info-item">
                        <i class="fas fa-clock"></i>
                        <span>创建于: 2024-04-05</span>
                    </div>
                    <div class="rule-info-item">
                        <i class="fas fa-bolt"></i>
                        <span>触发: 3次</span>
                    </div>
                </div>
                <div class="rule-actions">
                    <div class="rule-action-btn">
                        <i class="fas fa-history mr-1"></i> 历史记录
                    </div>
                    <div class="rule-action-btn primary">
                        <i class="fas fa-edit mr-1"></i> 编辑规则
                    </div>
                </div>
            </div>
        </div>

        <div class="rule-card">
            <div class="rule-header">
                <div class="rule-name">安防监控规则</div>
                <label class="rule-toggle">
                    <input type="checkbox" checked>
                    <span class="toggle-slider"></span>
                </label>
            </div>
            <div class="rule-content">
                <div class="rule-description">
                    非工作时间检测到人员活动时触发安防告警
                </div>
                <div class="rule-info">
                    <div class="rule-info-item">
                        <i class="fas fa-clock"></i>
                        <span>创建于: 2024-03-28</span>
                    </div>
                    <div class="rule-info-item">
                        <i class="fas fa-bolt"></i>
                        <span>触发: 5次</span>
                    </div>
                </div>
                <div class="rule-actions">
                    <div class="rule-action-btn">
                        <i class="fas fa-history mr-1"></i> 历史记录
                    </div>
                    <div class="rule-action-btn primary">
                        <i class="fas fa-edit mr-1"></i> 编辑规则
                    </div>
                </div>
            </div>
        </div>
        </div>

        <!-- 规则日志标签内容 -->
        <div id="logs-tab" class="tab-content">
            <!-- 日志筛选器 -->
            <div class="log-filters">
                <div class="log-filter active">全部</div>
                <div class="log-filter">今天</div>
                <div class="log-filter">昨天</div>
                <div class="log-filter">本周</div>
                <div class="log-filter">本月</div>
                <div class="log-filter">成功</div>
                <div class="log-filter">警告</div>
                <div class="log-filter">错误</div>
            </div>

            <!-- 日志列表 -->
            <div class="log-item">
                <div class="log-header">
                    <div class="log-rule-name">温度异常告警</div>
                    <div class="log-time">今天 14:35</div>
                </div>
                <div class="log-content">
                    检测到主楼3楼温度达到32°C，超过预设阈值(30°C)
                </div>
                <div class="log-status warning">已触发</div>
                <div class="log-details">
                    <i class="fas fa-chevron-right"></i> 查看详情
                </div>
            </div>

            <div class="log-item">
                <div class="log-header">
                    <div class="log-rule-name">人流量统计</div>
                    <div class="log-time">今天 14:00</div>
                </div>
                <div class="log-content">
                    已完成14:00时段人流量统计，共检测到127人次
                </div>
                <div class="log-status success">执行成功</div>
                <div class="log-details">
                    <i class="fas fa-chevron-right"></i> 查看详情
                </div>
            </div>

            <div class="log-item">
                <div class="log-header">
                    <div class="log-rule-name">安防监控规则</div>
                    <div class="log-time">今天 02:17</div>
                </div>
                <div class="log-content">
                    非工作时间检测到东区停车场有人员活动
                </div>
                <div class="log-status warning">已触发</div>
                <div class="log-details">
                    <i class="fas fa-chevron-right"></i> 查看详情
                </div>
            </div>

            <div class="log-item">
                <div class="log-header">
                    <div class="log-rule-name">能耗异常检测</div>
                    <div class="log-time">昨天 19:42</div>
                </div>
                <div class="log-content">
                    主楼空调系统能耗突增50%，可能存在设备故障
                </div>
                <div class="log-status error">需要处理</div>
                <div class="log-details">
                    <i class="fas fa-chevron-right"></i> 查看详情
                </div>
            </div>

            <div class="log-item">
                <div class="log-header">
                    <div class="log-rule-name">人流量统计</div>
                    <div class="log-time">昨天 13:00</div>
                </div>
                <div class="log-content">
                    已完成13:00时段人流量统计，共检测到215人次
                </div>
                <div class="log-status success">执行成功</div>
                <div class="log-details">
                    <i class="fas fa-chevron-right"></i> 查看详情
                </div>
            </div>
        </div>
    </div>

    <!-- 添加按钮 -->
    <a href="#" class="add-button animate-pulse">
        <i class="fas fa-plus"></i>
    </a>

    <!-- Navigation Bar -->
    <div class="nav-bar">
        <a href="home.html" class="nav-item">
            <i class="nav-icon fas fa-home"></i>
            <span>首页</span>
        </a>
        <a href="modules.html" class="nav-item active">
            <i class="nav-icon fas fa-th-large"></i>
            <span>功能</span>
        </a>
        <a href="users.html" class="nav-item">
            <i class="nav-icon fas fa-cog"></i>
            <span>系统</span>
        </a>
        <a href="profile.html" class="nav-item">
            <i class="nav-icon fas fa-user"></i>
            <span>我的</span>
        </a>
    </div>

    <script>
        function switchTab(tabName) {
            // 隐藏所有内容
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            // 取消所有标签的选中状态
            document.querySelectorAll('.segment-item').forEach(item => {
                item.classList.remove('active');
            });

            // 显示选中的内容
            document.getElementById(tabName + '-tab').classList.add('active');

            // 设置选中标签的样式
            event.currentTarget.classList.add('active');
        }
    </script>
</body>
</html>
