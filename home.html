<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>首页 - 项目管理平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        .project-selector {
            background-color: rgba(0, 122, 255, 0.1);
            border-radius: 12px;
            padding: 10px 16px;
            display: flex;
            align-items: center;
            gap: 10px;
            color: var(--primary-color);
            font-weight: 600;
            box-shadow: 0 2px 8px rgba(0, 122, 255, 0.15);
            transition: all var(--transition-fast);
        }

        .project-selector:active {
            transform: scale(0.98);
            box-shadow: 0 1px 4px rgba(0, 122, 255, 0.1);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 12px;
            margin-bottom: 24px;
        }

        .stat-card {
            background-color: white;
            border-radius: 16px;
            padding: 16px;
            text-align: center;
            box-shadow: var(--shadow-md);
            transition: transform var(--transition-fast);
            border: 1px solid rgba(0, 0, 0, 0.03);
        }

        .stat-card:active {
            transform: translateY(2px);
        }

        .stat-value {
            font-size: 28px;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 13px;
            color: var(--dark-gray);
        }

        .project-preview {
            height: 180px;
            background-image: url('https://images.unsplash.com/photo-1522071820081-009f0129c71c?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80');
            background-size: cover;
            background-position: center;
            border-radius: 16px;
            margin-bottom: 24px;
            position: relative;
            overflow: hidden;
            box-shadow: var(--shadow-lg);
        }

        .project-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(to top, rgba(0,0,0,0.8), transparent);
            padding: 20px;
            color: white;
        }

        .feature-list {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 16px;
            margin-bottom: 24px;
        }

        .feature-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            transition: transform var(--transition-fast);
        }

        .feature-item:active {
            transform: scale(0.95);
        }

        .feature-icon {
            width: 56px;
            height: 56px;
            background-color: rgba(0, 122, 255, 0.1);
            border-radius: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-color);
            font-size: 24px;
            margin-bottom: 8px;
            box-shadow: 0 4px 12px rgba(0, 122, 255, 0.15);
        }

        .feature-name {
            font-size: 13px;
            font-weight: 500;
            color: var(--text-color);
        }

        .project-card {
            border-radius: 16px;
            overflow: hidden;
            margin-bottom: 16px;
            box-shadow: var(--shadow-md);
            background-color: white;
            transition: transform var(--transition-fast);
            border: 1px solid rgba(0, 0, 0, 0.03);
        }

        .project-card:active {
            transform: translateY(2px);
            box-shadow: var(--shadow-sm);
        }

        .project-card-header {
            height: 100px;
            background-size: cover;
            background-position: center;
            position: relative;
        }

        .project-card-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 16px;
            background: linear-gradient(to top, rgba(0,0,0,0.7), transparent);
            color: white;
        }

        .project-card-content {
            padding: 16px;
        }

        .project-card-title {
            font-size: 17px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .project-card-subtitle {
            font-size: 14px;
            color: var(--dark-gray);
        }

        .project-card-stats {
            display: flex;
            margin-top: 12px;
            gap: 16px;
        }

        .project-card-stat {
            display: flex;
            align-items: center;
            font-size: 13px;
            color: var(--dark-gray);
        }

        .project-card-stat i {
            margin-right: 4px;
            font-size: 14px;
            color: var(--primary-color);
        }
    </style>
</head>
<body>
    <!-- Status Bar -->
    <div class="status-bar">
        <div>9:41</div>
        <div class="status-bar-icons">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>

    <!-- Header -->
    <div class="header">
        <div class="header-title">首页</div>
        <div class="header-right">
            <i class="fas fa-bell text-gray-600"></i>
        </div>
    </div>

    <!-- Content -->
    <div class="content">
        <!-- Project Selector -->
        <div class="flex justify-between items-center mb-6">
            <div class="project-selector">
                <i class="fas fa-project-diagram"></i>
                <span>智慧园区项目</span>
                <i class="fas fa-chevron-down text-xs"></i>
            </div>
            <button class="btn btn-secondary py-2 px-4 text-sm">
                <i class="fas fa-plus mr-1"></i> 新建项目
            </button>
        </div>

        <!-- Project Preview -->
        <div class="project-preview animate-fade-in">
            <div class="project-overlay">
                <div class="flex justify-between items-center">
                    <div>
                        <h3 class="text-xl font-semibold">智慧园区项目</h3>
                        <p class="text-sm opacity-80">进度: 65% · 截止日期: 2024-08-15</p>
                    </div>
                    <button class="btn btn-primary py-2 px-4 text-sm">
                        查看详情
                    </button>
                </div>
            </div>
        </div>

        <!-- Stats -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value">8</div>
                <div class="stat-label">进行中项目</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">24</div>
                <div class="stat-label">团队成员</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">12</div>
                <div class="stat-label">待处理任务</div>
            </div>
        </div>

        <!-- Quick Access -->
        <div class="card mb-6">
            <div class="card-header">
                <div class="flex justify-between items-center">
                    <span>快捷功能</span>
                    <span class="badge badge-primary">项目管理</span>
                </div>
            </div>
            <div class="card-content">
                <div class="feature-list">
                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="fas fa-tasks"></i>
                        </div>
                        <div class="feature-name">任务管理</div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="feature-name">团队协作</div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <div class="feature-name">项目日程</div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="feature-name">数据分析</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Projects -->
        <div class="card-header mb-4">
            <div class="flex justify-between items-center">
                <span>最近项目</span>
                <a href="#" class="text-primary-500 text-sm">查看全部</a>
            </div>
        </div>

        <div class="project-card animate-slide-up">
            <div class="project-card-header" style="background-image: url('https://images.unsplash.com/photo-1497366754035-f200968a6e72?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80')">
                <div class="project-card-overlay">
                    <div class="badge badge-success">进行中</div>
                </div>
            </div>
            <div class="project-card-content">
                <div class="project-card-title">智慧园区项目</div>
                <div class="project-card-subtitle">智能化园区管理解决方案</div>
                <div class="project-card-stats">
                    <div class="project-card-stat">
                        <i class="fas fa-calendar-alt"></i>
                        <span>截止: 2024-08-15</span>
                    </div>
                    <div class="project-card-stat">
                        <i class="fas fa-users"></i>
                        <span>12名成员</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="project-card animate-slide-up" style="animation-delay: 0.1s">
            <div class="project-card-header" style="background-image: url('https://images.unsplash.com/photo-1531482615713-2afd69097998?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80')">
                <div class="project-card-overlay">
                    <div class="badge badge-warning">规划中</div>
                </div>
            </div>
            <div class="project-card-content">
                <div class="project-card-title">能源管理系统</div>
                <div class="project-card-subtitle">企业能源消耗监控与优化</div>
                <div class="project-card-stats">
                    <div class="project-card-stat">
                        <i class="fas fa-calendar-alt"></i>
                        <span>开始: 2024-07-01</span>
                    </div>
                    <div class="project-card-stat">
                        <i class="fas fa-users"></i>
                        <span>8名成员</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation Bar -->
    <div class="nav-bar">
        <a href="home.html" class="nav-item active">
            <i class="nav-icon fas fa-home"></i>
            <span>首页</span>
        </a>
        <a href="modules.html" class="nav-item">
            <i class="nav-icon fas fa-th-large"></i>
            <span>功能</span>
        </a>
        <a href="users.html" class="nav-item">
            <i class="nav-icon fas fa-cog"></i>
            <span>系统</span>
        </a>
        <a href="profile.html" class="nav-item">
            <i class="nav-icon fas fa-user"></i>
            <span>我的</span>
        </a>
    </div>
</body>
</html>
