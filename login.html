<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - 项目管理平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        .login-container {
            display: flex;
            flex-direction: column;
            justify-content: center;
            height: 100vh;
            padding: 0 24px;
            background-color: #ffffff;
            background-image: url('https://images.unsplash.com/photo-1522071820081-009f0129c71c?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80');
            background-size: cover;
            background-position: center;
            position: relative;
        }

        .login-container::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255, 255, 255, 0.85);
            z-index: 1;
        }

        .login-content {
            position: relative;
            z-index: 2;
        }

        .login-logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 32px;
            background-color: var(--primary-color);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 40px;
            box-shadow: 0 8px 20px rgba(0, 122, 255, 0.3);
        }

        .login-title {
            text-align: center;
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 24px;
            color: #333;
        }

        .login-subtitle {
            text-align: center;
            font-size: 16px;
            color: #666;
            margin-bottom: 32px;
        }

        .login-form {
            background-color: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
        }

        .role-selector {
            display: flex;
            margin-bottom: 24px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .role-option {
            flex: 1;
            padding: 16px;
            text-align: center;
            background-color: var(--light-gray);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .role-option.active {
            background-color: var(--primary-color);
            color: white;
        }

        .role-option:first-child {
            border-right: 1px solid rgba(0, 0, 0, 0.05);
        }

        .role-icon {
            font-size: 24px;
            margin-bottom: 8px;
        }

        .role-name {
            font-weight: 600;
        }

        .role-description {
            font-size: 12px;
            opacity: 0.8;
            margin-top: 4px;
        }

        .role-option::after {
            content: "";
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 3px;
            background-color: var(--primary-color);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .role-option.active::after {
            transform: scaleX(1);
        }
    </style>
</head>
<body>
    <!-- Status Bar -->
    <div class="status-bar">
        <div>9:41</div>
        <div class="status-bar-icons">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>

    <div class="login-container">
        <div class="login-content">
            <div class="login-logo">
                <i class="fas fa-project-diagram"></i>
            </div>
            <h1 class="login-title">项目管理平台</h1>
            <p class="login-subtitle">高效协作，智能管理，助力项目成功</p>

            <div class="login-form animate-fade-in">
                <!-- 角色选择 -->
                <div class="role-selector">
                    <div class="role-option active" onclick="selectRole(this, 'integrator')">
                        <div class="role-icon">
                            <i class="fas fa-building"></i>
                        </div>
                        <div class="role-name">集成商</div>
                        <div class="role-description">管理项目和资源</div>
                    </div>
                    <div class="role-option" onclick="selectRole(this, 'member')">
                        <div class="role-icon">
                            <i class="fas fa-user-hard-hat"></i>
                        </div>
                        <div class="role-name">项目成员</div>
                        <div class="role-description">参与项目协作</div>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label" for="username">用户名</label>
                    <div class="relative">
                        <span class="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500">
                            <i class="fas fa-user"></i>
                        </span>
                        <input type="text" id="username" class="form-control pl-10" placeholder="请输入用户名">
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label" for="password">密码</label>
                    <div class="relative">
                        <span class="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500">
                            <i class="fas fa-lock"></i>
                        </span>
                        <input type="password" id="password" class="form-control pl-10" placeholder="请输入密码">
                    </div>
                </div>

                <div class="flex justify-between items-center mb-6">
                    <div class="flex items-center">
                        <input type="checkbox" id="remember" class="mr-2">
                        <label for="remember" class="text-sm text-gray-600">记住我</label>
                    </div>
                    <a href="forgot-password.html" class="text-sm text-blue-500">忘记密码?</a>
                </div>

                <button class="btn btn-primary btn-block mb-4">登 录</button>

                <p class="text-center text-sm text-gray-600">
                    还没有账号? <a href="#" class="text-blue-500">联系管理员</a>
                </p>
            </div>

            <div class="mt-8 text-center text-xs text-gray-500">
                © 2024 项目管理平台 版权所有
            </div>

            <script>
                function selectRole(element, role) {
                    // 移除所有选项的active类
                    document.querySelectorAll('.role-option').forEach(option => {
                        option.classList.remove('active');
                    });

                    // 为当前选中的选项添加active类
                    element.classList.add('active');

                    // 可以在这里设置一个隐藏字段或者其他方式记录选择的角色
                    console.log('Selected role:', role);
                }
            </script>
        </div>
    </div>
</body>
</html>
