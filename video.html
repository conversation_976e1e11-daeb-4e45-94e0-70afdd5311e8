<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>监控视频管理 - 集成商登陆平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        .search-bar {
            display: flex;
            align-items: center;
            background-color: var(--light-gray);
            border-radius: 10px;
            padding: 8px 12px;
            margin-bottom: 16px;
        }

        .search-bar input {
            flex: 1;
            border: none;
            background: transparent;
            margin-left: 8px;
            font-size: 15px;
        }

        .search-bar input:focus {
            outline: none;
        }

        .segment-control {
            display: flex;
            background-color: var(--light-gray);
            border-radius: 10px;
            padding: 4px;
            margin-bottom: 20px;
        }

        .segment-item {
            flex: 1;
            text-align: center;
            padding: 8px 0;
            font-size: 14px;
            border-radius: 8px;
        }

        .segment-item.active {
            background-color: white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            font-weight: 600;
        }

        .filter-tabs {
            display: flex;
            overflow-x: auto;
            margin-bottom: 16px;
            -webkit-overflow-scrolling: touch;
        }

        .filter-tab {
            padding: 8px 16px;
            border-radius: 100px;
            font-size: 14px;
            white-space: nowrap;
            margin-right: 8px;
        }

        .filter-tab.active {
            background-color: var(--primary-color);
            color: white;
        }

        .filter-tab:not(.active) {
            background-color: var(--light-gray);
            color: var(--dark-gray);
        }

        .video-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
            margin-bottom: 24px;
        }

        .video-card {
            background-color: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: var(--shadow-md);
            transition: transform var(--transition-fast);
        }

        .video-card:active {
            transform: scale(0.98);
        }

        .video-thumbnail {
            position: relative;
            height: 100px;
            background-size: cover;
            background-position: center;
        }

        .video-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(to bottom, rgba(0,0,0,0.1), rgba(0,0,0,0.5));
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .video-play {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(4px);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }

        .video-status {
            position: absolute;
            top: 8px;
            right: 8px;
            padding: 4px 8px;
            border-radius: 100px;
            font-size: 10px;
            font-weight: 600;
            background-color: rgba(0, 0, 0, 0.5);
            color: white;
        }

        .video-status.online {
            background-color: rgba(52, 199, 89, 0.8);
        }

        .video-status.offline {
            background-color: rgba(255, 59, 48, 0.8);
        }

        .video-status.recording {
            background-color: rgba(255, 149, 0, 0.8);
        }

        .video-info {
            padding: 12px;
        }

        .video-name {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 4px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .video-location {
            font-size: 12px;
            color: var(--dark-gray);
            display: flex;
            align-items: center;
        }

        .video-location i {
            font-size: 10px;
            margin-right: 4px;
        }

        .video-time {
            font-size: 12px;
            color: var(--dark-gray);
            margin-top: 4px;
            display: flex;
            align-items: center;
        }

        .video-time i {
            font-size: 10px;
            margin-right: 4px;
        }

        .video-player {
            width: 100%;
            height: 200px;
            background-color: #000;
            border-radius: 12px;
            overflow: hidden;
            margin-bottom: 16px;
            position: relative;
        }

        .video-player img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .player-controls {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 16px;
            background: linear-gradient(to top, rgba(0,0,0,0.7), transparent);
            display: flex;
            align-items: center;
            justify-content: space-between;
            color: white;
        }

        .player-control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
        }

        .player-control-btn.play {
            width: 48px;
            height: 48px;
            font-size: 18px;
        }

        .player-progress {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 3px;
            background-color: rgba(255, 255, 255, 0.3);
        }

        .progress-bar {
            height: 100%;
            width: 35%;
            background-color: var(--primary-color);
        }

        .section-title {
            font-size: 17px;
            font-weight: 600;
            margin-bottom: 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .section-title a {
            font-size: 14px;
            font-weight: normal;
            color: var(--primary-color);
        }

        /* 标签内容区域 */
        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* 录像回放样式 */
        .recording-card {
            background-color: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: var(--shadow-md);
            margin-bottom: 16px;
        }

        .recording-header {
            padding: 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid var(--light-gray);
        }

        .recording-title {
            display: flex;
            align-items: center;
        }

        .recording-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 18px;
            color: white;
            background: linear-gradient(135deg, #FF9500, #FF2D55);
        }

        .recording-name {
            font-size: 16px;
            font-weight: 600;
        }

        .recording-time {
            font-size: 13px;
            color: var(--dark-gray);
        }

        .recording-status {
            font-size: 12px;
            padding: 4px 10px;
            border-radius: 100px;
        }

        .recording-status.normal {
            background-color: rgba(52, 199, 89, 0.1);
            color: var(--success-color);
        }

        .recording-status.deleted {
            background-color: rgba(255, 59, 48, 0.1);
            color: var(--danger-color);
        }

        .recording-status.archived {
            background-color: rgba(0, 122, 255, 0.1);
            color: var(--primary-color);
        }

        .recording-content {
            padding: 12px;
        }

        .recording-info {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            margin-bottom: 12px;
        }

        .recording-info-item {
            display: flex;
            align-items: center;
            font-size: 13px;
            color: var(--dark-gray);
        }

        .recording-info-item i {
            margin-right: 6px;
            color: var(--primary-color);
            font-size: 12px;
        }

        .recording-preview {
            height: 120px;
            background-color: var(--light-gray);
            border-radius: 8px;
            margin-bottom: 12px;
            position: relative;
            overflow: hidden;
        }

        .recording-preview img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .recording-preview-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(to bottom, rgba(0,0,0,0.1), rgba(0,0,0,0.5));
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .recording-preview-play {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(4px);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
        }

        .recording-actions {
            display: flex;
            gap: 8px;
        }

        .recording-action-btn {
            flex: 1;
            padding: 8px 0;
            border-radius: 8px;
            font-size: 13px;
            text-align: center;
            background-color: var(--light-gray);
            color: var(--dark-gray);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .recording-action-btn i {
            margin-right: 6px;
        }

        .recording-action-btn.primary {
            background-color: rgba(0, 122, 255, 0.1);
            color: var(--primary-color);
        }

        .recording-action-btn.danger {
            background-color: rgba(255, 59, 48, 0.1);
            color: var(--danger-color);
        }

        /* 设备管理样式 */
        .device-card {
            background-color: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: var(--shadow-md);
            margin-bottom: 16px;
        }

        .device-header {
            padding: 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid var(--light-gray);
        }

        .device-title {
            display: flex;
            align-items: center;
        }

        .device-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 18px;
            color: white;
            background: linear-gradient(135deg, #007AFF, #5AC8FA);
        }

        .device-name {
            font-size: 16px;
            font-weight: 600;
        }

        .device-id {
            font-size: 13px;
            color: var(--dark-gray);
        }

        .device-status {
            font-size: 12px;
            padding: 4px 10px;
            border-radius: 100px;
        }

        .device-status.online {
            background-color: rgba(52, 199, 89, 0.1);
            color: var(--success-color);
        }

        .device-status.offline {
            background-color: rgba(255, 59, 48, 0.1);
            color: var(--danger-color);
        }

        .device-status.maintenance {
            background-color: rgba(255, 149, 0, 0.1);
            color: var(--warning-color);
        }

        .device-content {
            padding: 12px;
        }

        .device-info {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            margin-bottom: 12px;
        }

        .device-info-item {
            display: flex;
            align-items: center;
            font-size: 13px;
            color: var(--dark-gray);
        }

        .device-info-item i {
            margin-right: 6px;
            color: var(--primary-color);
            font-size: 12px;
        }

        .device-stats {
            background-color: var(--light-gray);
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 12px;
        }

        .device-stat-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 13px;
        }

        .device-stat-item:last-child {
            margin-bottom: 0;
        }

        .device-stat-label {
            color: var(--dark-gray);
        }

        .device-stat-value {
            font-weight: 500;
        }

        .device-stat-value.good {
            color: var(--success-color);
        }

        .device-stat-value.warning {
            color: var(--warning-color);
        }

        .device-stat-value.danger {
            color: var(--danger-color);
        }

        .device-actions {
            display: flex;
            gap: 8px;
        }

        .device-action-btn {
            flex: 1;
            padding: 8px 0;
            border-radius: 8px;
            font-size: 13px;
            text-align: center;
            background-color: var(--light-gray);
            color: var(--dark-gray);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .device-action-btn i {
            margin-right: 6px;
        }

        .device-action-btn.primary {
            background-color: rgba(0, 122, 255, 0.1);
            color: var(--primary-color);
        }

        .device-action-btn.danger {
            background-color: rgba(255, 59, 48, 0.1);
            color: var(--danger-color);
        }

        /* 浮动添加按钮样式 */
        .add-button {
            position: fixed;
            bottom: 100px;
            right: 20px;
            width: 56px;
            height: 56px;
            border-radius: 50%;
            background-color: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
            z-index: 50;
            transition: all var(--transition-fast);
        }

        .add-button:active {
            transform: scale(0.95);
            box-shadow: 0 2px 8px rgba(0, 122, 255, 0.2);
        }
    </style>
</head>
<body>
    <!-- Status Bar -->
    <div class="status-bar">
        <div>9:41</div>
        <div class="status-bar-icons">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>

    <!-- Header -->
    <div class="header">
        <div class="header-title">监控视频管理</div>
        <div class="header-right">
            <i class="fas fa-ellipsis-v text-gray-600"></i>
        </div>
    </div>

    <!-- Content -->
    <div class="content">
        <!-- Search Bar -->
        <div class="search-bar">
            <i class="fas fa-search text-gray-400"></i>
            <input type="text" placeholder="搜索摄像头或录像...">
        </div>

        <!-- Segment Control -->
        <div class="segment-control">
            <div class="segment-item active">实时监控</div>
            <div class="segment-item">录像回放</div>
            <div class="segment-item">设备管理</div>
        </div>

        <!-- Filter Tabs -->
        <div class="filter-tabs">
            <div class="filter-tab active">全部</div>
            <div class="filter-tab">在线</div>
            <div class="filter-tab">离线</div>
            <div class="filter-tab">录制中</div>
            <div class="filter-tab">室内</div>
            <div class="filter-tab">室外</div>
        </div>

        <!-- Featured Video -->
        <div class="video-player">
            <img src="https://images.unsplash.com/photo-1590856029826-c7a73142bbf1?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80" alt="Video Preview">
            <div class="player-controls">
                <div class="player-control-btn">
                    <i class="fas fa-step-backward"></i>
                </div>
                <div class="player-control-btn play">
                    <i class="fas fa-play"></i>
                </div>
                <div class="player-control-btn">
                    <i class="fas fa-step-forward"></i>
                </div>
            </div>
            <div class="player-progress">
                <div class="progress-bar"></div>
            </div>
        </div>

        <!-- Camera Groups -->
        <div class="section-title">
            <span>A栋摄像头</span>
            <a href="#">查看全部</a>
        </div>

        <div class="video-grid">
            <div class="video-card">
                <div class="video-thumbnail" style="background-image: url('https://images.unsplash.com/photo-1590856029826-c7a73142bbf1?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80')">
                    <div class="video-overlay">
                        <div class="video-play">
                            <i class="fas fa-play"></i>
                        </div>
                    </div>
                    <div class="video-status online">在线</div>
                </div>
                <div class="video-info">
                    <div class="video-name">A栋大厅摄像头</div>
                    <div class="video-location">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>A栋1楼大厅</span>
                    </div>
                    <div class="video-time">
                        <i class="fas fa-clock"></i>
                        <span>实时</span>
                    </div>
                </div>
            </div>

            <div class="video-card">
                <div class="video-thumbnail" style="background-image: url('https://images.unsplash.com/photo-1621886292650-520f76c747d6?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80')">
                    <div class="video-overlay">
                        <div class="video-play">
                            <i class="fas fa-play"></i>
                        </div>
                    </div>
                    <div class="video-status recording">录制中</div>
                </div>
                <div class="video-info">
                    <div class="video-name">A栋电梯间</div>
                    <div class="video-location">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>A栋电梯间</span>
                    </div>
                    <div class="video-time">
                        <i class="fas fa-clock"></i>
                        <span>实时</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="section-title">
            <span>B栋摄像头</span>
            <a href="#">查看全部</a>
        </div>

        <div class="video-grid">
            <div class="video-card">
                <div class="video-thumbnail" style="background-image: url('https://images.unsplash.com/photo-1519575706483-221027bfbb31?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80')">
                    <div class="video-overlay">
                        <div class="video-play">
                            <i class="fas fa-play"></i>
                        </div>
                    </div>
                    <div class="video-status online">在线</div>
                </div>
                <div class="video-info">
                    <div class="video-name">B栋会议室</div>
                    <div class="video-location">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>B栋2楼会议室</span>
                    </div>
                    <div class="video-time">
                        <i class="fas fa-clock"></i>
                        <span>实时</span>
                    </div>
                </div>
            </div>

            <div class="video-card">
                <div class="video-thumbnail" style="background-image: url('https://images.unsplash.com/photo-1557683304-673a23048d34?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80')">
                    <div class="video-overlay">
                        <div class="video-play">
                            <i class="fas fa-play"></i>
                        </div>
                    </div>
                    <div class="video-status offline">离线</div>
                </div>
                <div class="video-info">
                    <div class="video-name">B栋走廊</div>
                    <div class="video-location">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>B栋3楼走廊</span>
                    </div>
                    <div class="video-time">
                        <i class="fas fa-clock"></i>
                        <span>离线 (2小时前)</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation Bar -->
    <div class="nav-bar">
        <a href="home.html" class="nav-item">
            <i class="nav-icon fas fa-home"></i>
            <span>首页</span>
        </a>
        <a href="modules.html" class="nav-item active">
            <i class="nav-icon fas fa-th-large"></i>
            <span>功能</span>
        </a>
        <a href="users.html" class="nav-item">
            <i class="nav-icon fas fa-cog"></i>
            <span>系统</span>
        </a>
        <a href="profile.html" class="nav-item">
            <i class="nav-icon fas fa-user"></i>
            <span>我的</span>
        </a>
    </div>
</body>
</html>
