<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新增网关 - 项目管理平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        .form-container {
            padding: 16px;
        }
        
        .form-section {
            margin-bottom: 24px;
        }
        
        .form-section-title {
            font-size: 17px;
            font-weight: 600;
            margin-bottom: 16px;
            color: var(--text-color);
            display: flex;
            align-items: center;
        }
        
        .form-section-title i {
            margin-right: 8px;
            color: var(--primary-color);
            font-size: 18px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            font-size: 15px;
            font-weight: 500;
        }
        
        .required-mark {
            color: var(--danger-color);
            margin-left: 4px;
        }
        
        .form-control {
            width: 100%;
            padding: 12px 16px;
            border-radius: 12px;
            border: 1px solid var(--medium-gray);
            font-size: 16px;
            background-color: var(--background-color);
            transition: all var(--transition-fast);
        }
        
        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
        }
        
        .form-select {
            appearance: none;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%238e8e93'%3E%3Cpath d='M7 10l5 5 5-5z'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 12px center;
            background-size: 20px;
            padding-right: 40px;
        }
        
        .form-textarea {
            min-height: 120px;
            resize: vertical;
        }
        
        .form-hint {
            font-size: 13px;
            color: var(--dark-gray);
            margin-top: 4px;
        }
        
        .form-switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 28px;
        }
        
        .form-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .switch-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: var(--medium-gray);
            transition: .4s;
            border-radius: 34px;
        }
        
        .switch-slider:before {
            position: absolute;
            content: "";
            height: 24px;
            width: 24px;
            left: 2px;
            bottom: 2px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        input:checked + .switch-slider {
            background-color: var(--primary-color);
        }
        
        input:checked + .switch-slider:before {
            transform: translateX(22px);
        }
        
        .switch-label {
            font-size: 15px;
            margin-left: 12px;
        }
        
        .form-actions {
            display: flex;
            gap: 12px;
            margin-top: 32px;
        }
        
        .btn-block {
            flex: 1;
        }
    </style>
</head>
<body>
    <!-- Status Bar -->
    <div class="status-bar">
        <div>9:41</div>
        <div class="status-bar-icons">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>

    <!-- Header -->
    <div class="header">
        <div class="header-left">
            <a href="gateway.html" class="text-gray-600">
                <i class="fas fa-arrow-left"></i>
            </a>
        </div>
        <div class="header-title">新增网关</div>
        <div class="header-right">
            <i class="fas fa-check text-primary-500"></i>
        </div>
    </div>

    <!-- Content -->
    <div class="content">
        <div class="form-container animate-fade-in">
            <div class="form-section">
                <div class="form-section-title">
                    <i class="fas fa-info-circle"></i>
                    <span>基本信息</span>
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="gateway-name">
                        网关名称
                        <span class="required-mark">*</span>
                    </label>
                    <input type="text" id="gateway-name" class="form-control" placeholder="请输入网关名称">
                    <div class="form-hint">例如：主楼网关、东区网关</div>
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="gateway-code">
                        网关编码
                        <span class="required-mark">*</span>
                    </label>
                    <input type="text" id="gateway-code" class="form-control" placeholder="请输入网关编码">
                    <div class="form-hint">例如：GW-001、GW-002</div>
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="gateway-brand">
                        网关品牌
                        <span class="required-mark">*</span>
                    </label>
                    <input type="text" id="gateway-brand" class="form-control" placeholder="请输入网关品牌">
                    <div class="form-hint">例如：华为、思科、中兴</div>
                </div>
            </div>
            
            <div class="form-section">
                <div class="form-section-title">
                    <i class="fas fa-cog"></i>
                    <span>配置信息</span>
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="gateway-space">
                        所属空间
                    </label>
                    <select id="gateway-space" class="form-control form-select">
                        <option value="">请选择所属空间</option>
                        <option value="1">主楼</option>
                        <option value="2">东区</option>
                        <option value="3">西区</option>
                        <option value="4">南区</option>
                        <option value="5">北区</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="gateway-protocol">
                        网关协议
                        <span class="required-mark">*</span>
                    </label>
                    <select id="gateway-protocol" class="form-control form-select">
                        <option value="">请选择网关协议</option>
                        <option value="1">Modbus</option>
                        <option value="2">BACnet</option>
                        <option value="3">MQTT</option>
                        <option value="4">OPC UA</option>
                        <option value="5">HTTP</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <div class="flex items-center">
                        <label class="form-switch">
                            <input type="checkbox" checked>
                            <span class="switch-slider"></span>
                        </label>
                        <span class="switch-label">状态</span>
                    </div>
                    <div class="form-hint ml-16">启用后网关将立即生效</div>
                </div>
            </div>
            
            <div class="form-section">
                <div class="form-section-title">
                    <i class="fas fa-align-left"></i>
                    <span>其他信息</span>
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="gateway-description">
                        网关描述
                    </label>
                    <textarea id="gateway-description" class="form-control form-textarea" placeholder="请输入网关描述信息"></textarea>
                    <div class="form-hint">描述网关的用途、位置等信息</div>
                </div>
            </div>
            
            <div class="form-actions">
                <button class="btn btn-secondary btn-block">取消</button>
                <button class="btn btn-primary btn-block">保存</button>
            </div>
        </div>
    </div>

    <!-- Navigation Bar -->
    <div class="nav-bar">
        <a href="home.html" class="nav-item">
            <i class="nav-icon fas fa-home"></i>
            <span>首页</span>
        </a>
        <a href="modules.html" class="nav-item active">
            <i class="nav-icon fas fa-th-large"></i>
            <span>功能</span>
        </a>
        <a href="users.html" class="nav-item">
            <i class="nav-icon fas fa-cog"></i>
            <span>系统</span>
        </a>
        <a href="profile.html" class="nav-item">
            <i class="nav-icon fas fa-user"></i>
            <span>我的</span>
        </a>
    </div>
</body>
</html>
