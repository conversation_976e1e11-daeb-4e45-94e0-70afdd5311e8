<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>崇实智慧数字项目平台 - 原型展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background-color: #f5f5f7;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
        }
        .prototype-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
            gap: 20px;
            padding: 20px;
        }
        .phone-frame {
            width: 375px;
            height: 812px;
            background-color: white;
            border-radius: 40px;
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            margin: 0 auto;
            position: relative;
            border: 10px solid #1a1a1a;
        }
        .screen-title {
            text-align: center;
            font-weight: 600;
            margin-bottom: 10px;
            font-size: 18px;
            color: #333;
        }
        iframe {
            width: 100%;
            height: 100%;
            border: none;
        }

        /* 登录页面说明样式 */
        .login-description {
            width: 375px;
            height: 812px;
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: auto;
            margin: 0 auto;
            padding: 20px;
            box-shadow: 0 2px 6px rgba(0,0,0,0.1);
        }

        .description-header {
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 1px solid #eee;
        }

        .description-header h3 {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
        }

        .description-subtitle {
            font-size: 14px;
            color: #666;
        }

        .description-content {
            padding: 0 5px;
        }

        .description-section {
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px dashed #eee;
        }

        .description-section:last-child {
            border-bottom: none;
        }

        .description-section h4 {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #007aff;
        }

        .description-section p {
            font-size: 14px;
            line-height: 1.5;
            color: #333;
            margin-bottom: 10px;
        }

        .description-section ul,
        .description-section ol {
            margin-left: 20px;
            margin-bottom: 10px;
        }

        .description-section li {
            font-size: 14px;
            line-height: 1.5;
            color: #333;
            margin-bottom: 5px;
        }

        .implementation-note {
            font-size: 13px;
            color: #666;
            background-color: #f8f9fa;
            padding: 8px 12px;
            border-left: 3px solid #007aff;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <header class="bg-gray-800 text-white p-4">
        <h1 class="text-2xl font-bold text-center">崇实智慧数字项目平台 - 原型展示</h1>
        <p class="text-center mt-2">高保真原型界面，模拟 iPhone 15 Pro 尺寸</p>
    </header>

    <div class="prototype-container">
        <!-- 登录页面 -->
        <div>
            <h2 class="screen-title">登录页面</h2>
            <div class="phone-frame">
                <iframe src="login.html"></iframe>
            </div>
        </div>

        <!-- 登录页面功能说明 -->
        <div>
            <h2 class="screen-title">登录页面说明</h2>
            <div class="login-description">
                <div class="description-header">
                    <h3>登录页面功能说明</h3>
                    <p class="description-subtitle">针对开发人员的实现指南</p>
                </div>

                <div class="description-content">
                    <div class="description-section">
                        <h4>1. 角色选择功能</h4>
                        <p>用户必须在登录前选择一个角色：</p>
                        <ul>
                            <li><strong>集成商：</strong> 拥有项目管理和资源分配权限</li>
                            <li><strong>项目成员：</strong> 拥有参与项目和执行任务的权限</li>
                        </ul>
                        <p class="implementation-note">实现要点：角色选择为单选，默认选中"集成商"角色</p>
                    </div>

                    <div class="description-section">
                        <h4>2. 登录表单</h4>
                        <ul>
                            <li><strong>用户名：</strong> 必填字段，需进行非空验证</li>
                            <li><strong>密码：</strong> 必填字段，需进行非空验证</li>
                            <li><strong>记住我：</strong> 可选复选框，用于保存登录状态</li>
                            <li><strong>忘记密码：</strong> 链接到密码重置流程</li>
                        </ul>
                        <p class="implementation-note">实现要点：表单提交前需验证所有必填字段</p>
                    </div>

                    <div class="description-section">
                        <h4>3. 登录按钮</h4>
                        <p>点击后需执行以下操作：</p>
                        <ol>
                            <li>验证表单数据完整性</li>
                            <li>发送登录请求，包含用户名、密码和所选角色</li>
                            <li>根据角色不同，登录成功后跳转到相应的首页视图</li>
                        </ol>
                        <p class="implementation-note">实现要点：登录过程中显示加载状态，禁用按钮防止重复提交</p>
                    </div>

                    <div class="description-section">
                        <h4>4. 错误处理</h4>
                        <p>需要处理以下错误情况：</p>
                        <ul>
                            <li>表单验证错误：显示相应字段下方的错误提示</li>
                            <li>登录失败：显示错误消息（用户名/密码错误、账号锁定等）</li>
                            <li>网络错误：显示网络连接问题提示</li>
                        </ul>
                    </div>

                    <div class="description-section">
                        <h4>5. 其他说明</h4>
                        <ul>
                            <li>页面需要适配移动端和桌面端</li>
                            <li>登录状态需要使用 Token 存储，支持 JWT</li>
                            <li>需实现自动登录功能（当选择"记住我"时）</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- 首页 -->
        <div>
            <h2 class="screen-title">首页</h2>
            <div class="phone-frame">
                <iframe src="home.html"></iframe>
            </div>
        </div>

        <!-- 首页功能说明 -->
        <div>
            <h2 class="screen-title">首页功能说明</h2>
            <div class="login-description">
                <div class="description-header">
                    <h3>首页功能说明</h3>
                    <p class="description-subtitle">针对开发人员的实现指南</p>
                </div>

                <div class="description-content">
                    <div class="description-section">
                        <h4>1. 项目选择器</h4>
                        <p>页面顶部的项目选择器组件：</p>
                        <ul>
                            <li><strong>当前项目显示：</strong> 显示当前选中的项目名称</li>
                            <li><strong>下拉功能：</strong> 点击后显示用户有权限的项目列表</li>
                            <li><strong>新建项目按钮：</strong> 点击后跳转到项目创建页面</li>
                        </ul>
                        <p class="implementation-note">实现要点：项目列表需从后端API获取，并根据用户权限过滤</p>
                    </div>

                    <div class="description-section">
                        <h4>2. 项目预览卡片</h4>
                        <p>大型项目预览卡片，展示当前项目信息：</p>
                        <ul>
                            <li><strong>项目名称：</strong> 当前选中项目的名称</li>
                            <li><strong>项目进度：</strong> 显示项目完成百分比</li>
                            <li><strong>截止日期：</strong> 显示项目截止日期</li>
                            <li><strong>查看详情按钮：</strong> 点击后跳转到项目详情页面</li>
                        </ul>
                        <p class="implementation-note">实现要点：进度计算应基于已完成任务数/总任务数</p>
                    </div>

                    <div class="description-section">
                        <h4>3. 统计卡片</h4>
                        <p>三个统计数据卡片，展示关键指标：</p>
                        <ul>
                            <li><strong>进行中项目：</strong> 显示用户参与的进行中项目数量</li>
                            <li><strong>团队成员：</strong> 显示当前项目的团队成员数量</li>
                            <li><strong>待处理任务：</strong> 显示分配给用户的待处理任务数量</li>
                        </ul>
                        <p class="implementation-note">实现要点：数据应实时从后端获取，支持点击跳转到相应详情页面</p>
                    </div>

                    <div class="description-section">
                        <h4>4. 快捷功能区</h4>
                        <p>四个快捷功能图标，提供常用功能入口：</p>
                        <ul>
                            <li><strong>任务管理：</strong> 跳转到任务管理页面</li>
                            <li><strong>团队协作：</strong> 跳转到团队协作页面</li>
                            <li><strong>项目日程：</strong> 跳转到项目日程页面</li>
                            <li><strong>数据分析：</strong> 跳转到数据分析页面</li>
                        </ul>
                        <p class="implementation-note">实现要点：图标应支持自定义配置，可根据用户常用功能动态调整</p>
                    </div>

                    <div class="description-section">
                        <h4>5. 最近项目列表</h4>
                        <p>展示用户最近访问或参与的项目：</p>
                        <ul>
                            <li><strong>项目卡片：</strong> 显示项目封面图、名称、描述</li>
                            <li><strong>项目状态：</strong> 显示项目当前状态（进行中、规划中等）</li>
                            <li><strong>项目信息：</strong> 显示截止日期、团队成员数等关键信息</li>
                        </ul>
                        <p class="implementation-note">实现要点：列表应支持下拉刷新和分页加载，点击卡片跳转到项目详情</p>
                    </div>

                    <div class="description-section">
                        <h4>6. 导航栏</h4>
                        <p>底部导航栏，提供主要功能区域入口：</p>
                        <ul>
                            <li><strong>首页：</strong> 当前页面，显示项目概览</li>
                            <li><strong>功能：</strong> 跳转到功能模块页面</li>
                            <li><strong>系统：</strong> 跳转到系统设置页面</li>
                            <li><strong>我的：</strong> 跳转到个人中心页面</li>
                        </ul>
                        <p class="implementation-note">实现要点：当前选中的导航项应高亮显示，支持徽标提示（如未读消息数）</p>
                    </div>

                    <div class="description-section">
                        <h4>7. 数据加载与刷新</h4>
                        <p>页面数据加载与刷新机制：</p>
                        <ul>
                            <li>首次进入页面时自动加载数据</li>
                            <li>支持下拉刷新更新所有数据</li>
                            <li>定时自动刷新关键数据（如待处理任务数）</li>
                        </ul>
                        <p class="implementation-note">实现要点：使用骨架屏(Skeleton)显示加载状态，避免空白页面</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 功能模块 -->
        <div>
            <h2 class="screen-title">功能模块</h2>
            <div class="phone-frame">
                <iframe src="modules.html"></iframe>
            </div>
        </div>

        <!-- 功能模块页面说明 -->
        <div>
            <h2 class="screen-title">功能模块页面说明</h2>
            <div class="login-description">
                <div class="description-header">
                    <h3>功能模块页面功能说明</h3>
                    <p class="description-subtitle">针对开发人员的实现指南</p>
                </div>

                <div class="description-content">
                    <div class="description-section">
                        <h4>1. 页面结构</h4>
                        <p>功能模块页面是平台的功能导航中心，包含以下主要部分：</p>
                        <ul>
                            <li><strong>顶部标题栏：</strong> 显示"功能模块"标题和搜索按钮</li>
                            <li><strong>最近使用区域：</strong> 显示用户最近访问的功能模块</li>
                            <li><strong>全部功能区域：</strong> 以网格形式展示所有可用功能模块</li>
                            <li><strong>底部导航栏：</strong> 提供主要功能区域的快速访问</li>
                        </ul>
                        <p class="implementation-note">实现要点：页面布局应采用弹性设计，适应不同屏幕尺寸</p>
                    </div>

                    <div class="description-section">
                        <h4>2. 最近使用区域</h4>
                        <p>展示用户最近访问的功能模块：</p>
                        <ul>
                            <li><strong>列表项结构：</strong> 图标、功能名称、访问时间、右侧箭头</li>
                            <li><strong>数据来源：</strong> 基于用户访问历史记录，按时间倒序排列</li>
                            <li><strong>最大显示数量：</strong> 显示最近5个访问的功能模块</li>
                            <li><strong>点击行为：</strong> 点击后跳转到对应功能页面</li>
                        </ul>
                        <p class="implementation-note">实现要点：需要在本地存储或服务器端记录用户访问历史，支持实时更新</p>
                    </div>

                    <div class="description-section">
                        <h4>3. 全部功能区域</h4>
                        <p>以网格形式展示所有可用的功能模块：</p>
                        <ul>
                            <li><strong>网格布局：</strong> 采用响应式网格，在不同屏幕尺寸下自动调整列数</li>
                            <li><strong>功能分类：</strong> 功能模块按类型分组（项目管理功能、设备管理功能）</li>
                            <li><strong>模块卡片：</strong> 每个功能模块包含图标、名称和简短描述</li>
                            <li><strong>视觉区分：</strong> 不同类型的功能模块使用不同的背景色和图标</li>
                        </ul>
                        <p class="implementation-note">实现要点：功能模块应根据用户权限动态显示，无权限的模块应隐藏或显示禁用状态</p>
                    </div>

                    <div class="description-section">
                        <h4>4. 功能模块卡片</h4>
                        <p>每个功能模块卡片的设计和交互：</p>
                        <ul>
                            <li><strong>图标设计：</strong> 使用渐变背景色和白色图标，提高视觉识别度</li>
                            <li><strong>文本信息：</strong> 包含功能名称（粗体）和简短描述（常规字体）</li>
                            <li><strong>点击效果：</strong> 点击时有轻微缩放和阴影变化，提供视觉反馈</li>
                            <li><strong>跳转行为：</strong> 点击后跳转到对应功能页面</li>
                        </ul>
                        <p class="implementation-note">实现要点：卡片设计应保持一致性，图标大小、文字大小和间距应统一</p>
                    </div>

                    <div class="description-section">
                        <h4>5. 搜索功能</h4>
                        <p>顶部搜索按钮的功能实现：</p>
                        <ul>
                            <li><strong>搜索范围：</strong> 搜索所有可用的功能模块</li>
                            <li><strong>搜索条件：</strong> 支持按功能名称和描述搜索</li>
                            <li><strong>搜索界面：</strong> 点击搜索按钮后展开搜索框，支持实时搜索</li>
                            <li><strong>搜索结果：</strong> 以列表形式展示匹配的功能模块</li>
                        </ul>
                        <p class="implementation-note">实现要点：搜索应支持模糊匹配和拼音搜索，提高用户搜索体验</p>
                    </div>

                    <div class="description-section">
                        <h4>6. 数据存储与更新</h4>
                        <p>功能模块数据的存储和更新机制：</p>
                        <ul>
                            <li><strong>模块配置：</strong> 功能模块的基本信息（名称、图标、描述等）应从服务器获取</li>
                            <li><strong>权限控制：</strong> 根据用户角色和权限过滤显示的功能模块</li>
                            <li><strong>访问记录：</strong> 记录用户访问历史，用于更新"最近使用"区域</li>
                            <li><strong>缓存策略：</strong> 功能模块基本信息可以缓存，减少服务器请求</li>
                        </ul>
                        <p class="implementation-note">实现要点：应实现数据更新机制，确保功能模块信息的及时更新</p>
                    </div>

                    <div class="description-section">
                        <h4>7. 交互与动效</h4>
                        <p>页面交互和动效设计：</p>
                        <ul>
                            <li><strong>页面加载：</strong> 页面加载时应有平滑的淡入效果</li>
                            <li><strong>卡片交互：</strong> 卡片点击时有轻微的缩放和阴影变化</li>
                            <li><strong>列表滚动：</strong> 支持平滑滚动，可考虑添加滚动到顶部按钮</li>
                            <li><strong>导航切换：</strong> 底部导航切换时应有平滑的过渡效果</li>
                        </ul>
                        <p class="implementation-note">实现要点：动效应适度，避免过度动画影响性能和用户体验</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 网关管理 -->
        <div>
            <h2 class="screen-title">网关管理</h2>
            <div class="phone-frame">
                <iframe src="gateway.html"></iframe>
            </div>
        </div>

        <!-- 网关管理页面说明 -->
        <div>
            <h2 class="screen-title">网关管理页面说明</h2>
            <div class="login-description">
                <div class="description-header">
                    <h3>网关管理页面功能说明</h3>
                    <p class="description-subtitle">针对开发人员的实现指南</p>
                </div>

                <div class="description-content">
                    <div class="description-section">
                        <h4>1. 页面结构</h4>
                        <p>网关管理页面用于管理和监控所有网关设备，包含以下主要部分：</p>
                        <ul>
                            <li><strong>顶部标题栏：</strong> 显示"网关管理"标题、返回按钮和搜索按钮</li>
                            <li><strong>搜索栏：</strong> 用于搜索特定网关</li>
                            <li><strong>筛选标签：</strong> 用于按状态筛选网关列表</li>
                            <li><strong>网关列表：</strong> 显示所有网关及其状态</li>
                            <li><strong>添加按钮：</strong> 浮动按钮，用于添加新网关</li>
                        </ul>
                        <p class="implementation-note">实现要点：页面应支持下拉刷新，更新网关状态和列表</p>
                    </div>

                    <div class="description-section">
                        <h4>2. 搜索功能</h4>
                        <p>搜索栏的功能实现：</p>
                        <ul>
                            <li><strong>搜索范围：</strong> 搜索网关名称、ID、IP地址等字段</li>
                            <li><strong>搜索方式：</strong> 实时搜索，输入时即时过滤结果</li>
                            <li><strong>搜索历史：</strong> 可选功能，记录用户最近的搜索关键词</li>
                            <li><strong>无结果处理：</strong> 当搜索无结果时显示提示信息</li>
                        </ul>
                        <p class="implementation-note">实现要点：搜索应支持模糊匹配和拼音搜索，提高用户搜索体验</p>
                    </div>

                    <div class="description-section">
                        <h4>3. 筛选标签</h4>
                        <p>筛选标签的功能实现：</p>
                        <ul>
                            <li><strong>标签类型：</strong> 全部、在线、离线、告警、维护中</li>
                            <li><strong>选中状态：</strong> 当前选中的标签高亮显示</li>
                            <li><strong>筛选逻辑：</strong> 点击标签后立即过滤网关列表</li>
                            <li><strong>数量指示：</strong> 可选功能，在标签上显示符合条件的网关数量</li>
                        </ul>
                        <p class="implementation-note">实现要点：筛选功能应与搜索功能协同工作，同时应用两种过滤条件</p>
                    </div>

                    <div class="description-section">
                        <h4>4. 网关卡片</h4>
                        <p>网关卡片的设计和功能：</p>
                        <ul>
                            <li><strong>基本信息：</strong> 显示网关名称、ID、IP地址等基本信息</li>
                            <li><strong>状态指示：</strong> 使用颜色和图标显示网关状态（在线、离线、告警）</li>
                            <li><strong>最后心跳：</strong> 显示网关最后一次通信的时间</li>
                            <li><strong>操作按钮：</strong> 右侧的更多操作按钮，点击显示操作菜单</li>
                        </ul>
                        <p class="implementation-note">实现要点：卡片点击应跳转到网关详情页面，长按可触发多选模式</p>
                    </div>

                    <div class="description-section">
                        <h4>5. 操作菜单</h4>
                        <p>点击网关卡片右侧的操作按钮后显示的菜单：</p>
                        <ul>
                            <li><strong>查看详情：</strong> 跳转到网关详情页面</li>
                            <li><strong>编辑网关：</strong> 跳转到网关编辑页面</li>
                            <li><strong>重启网关：</strong> 发送重启命令到网关</li>
                            <li><strong>删除网关：</strong> 从系统中删除该网关</li>
                        </ul>
                        <p class="implementation-note">实现要点：危险操作（如删除）应有二次确认机制，防止误操作</p>
                    </div>

                    <div class="description-section">
                        <h4>6. 添加网关按钮</h4>
                        <p>浮动添加按钮的功能实现：</p>
                        <ul>
                            <li><strong>位置：</strong> 固定在页面右下角，不随页面滚动而移动</li>
                            <li><strong>动画效果：</strong> 有轻微的脉动动画，提高可见性</li>
                            <li><strong>点击行为：</strong> 点击后跳转到新增网关页面</li>
                            <li><strong>权限控制：</strong> 根据用户权限决定是否显示此按钮</li>
                        </ul>
                        <p class="implementation-note">实现要点：按钮应有明显的点击反馈，且不应被其他元素遮挡</p>
                    </div>

                    <div class="description-section">
                        <h4>7. 网关状态更新</h4>
                        <p>网关状态的实时更新机制：</p>
                        <ul>
                            <li><strong>轮询更新：</strong> 定期从服务器获取网关状态更新</li>
                            <li><strong>推送更新：</strong> 通过WebSocket等技术接收实时状态变更</li>
                            <li><strong>状态变化：</strong> 网关状态变化时应有视觉提示（如颜色变化、闪烁等）</li>
                            <li><strong>告警通知：</strong> 网关发生告警时可显示通知或声音提醒</li>
                        </ul>
                        <p class="implementation-note">实现要点：状态更新应考虑网络性能，避免频繁请求导致性能问题</p>
                    </div>

                    <div class="description-section">
                        <h4>8. 批量操作</h4>
                        <p>支持对多个网关进行批量操作：</p>
                        <ul>
                            <li><strong>进入方式：</strong> 长按网关卡片进入多选模式</li>
                            <li><strong>选择指示：</strong> 被选中的网关卡片应有明显标记</li>
                            <li><strong>批量操作：</strong> 支持批量重启、删除等操作</li>
                            <li><strong>操作反馈：</strong> 批量操作完成后应显示操作结果</li>
                        </ul>
                        <p class="implementation-note">实现要点：批量操作应有进度指示，特别是操作大量网关时</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 新增网关 -->
        <div>
            <h2 class="screen-title">新增网关</h2>
            <div class="phone-frame">
                <iframe src="gateway-add.html"></iframe>
            </div>
        </div>

        <!-- 新增网关页面说明 -->
        <div>
            <h2 class="screen-title">新增网关页面说明</h2>
            <div class="login-description">
                <div class="description-header">
                    <h3>新增网关页面功能说明</h3>
                    <p class="description-subtitle">针对开发人员的实现指南</p>
                </div>

                <div class="description-content">
                    <div class="description-section">
                        <h4>1. 页面结构</h4>
                        <p>新增网关页面是一个表单页面，用于创建新的网关设备，包含以下主要部分：</p>
                        <ul>
                            <li><strong>顶部标题栏：</strong> 显示"新增网关"标题、返回按钮和保存按钮</li>
                            <li><strong>表单容器：</strong> 包含多个分组的表单字段</li>
                            <li><strong>基本信息区域：</strong> 包含网关名称、编码、品牌等基本信息字段</li>
                            <li><strong>配置信息区域：</strong> 包含所属空间、网关协议、状态等配置字段</li>
                            <li><strong>其他信息区域：</strong> 包含网关描述等附加信息字段</li>
                            <li><strong>操作按钮区域：</strong> 包含取消和保存按钮</li>
                        </ul>
                        <p class="implementation-note">实现要点：表单应支持键盘导航和Tab键切换字段，提高用户输入效率</p>
                    </div>

                    <div class="description-section">
                        <h4>2. 基本信息区域</h4>
                        <p>基本信息区域包含以下字段：</p>
                        <ul>
                            <li><strong>网关名称（必填）：</strong> 文本输入框，用于输入网关名称</li>
                            <li><strong>网关编码（必填）：</strong> 文本输入框，用于输入网关唯一编码</li>
                            <li><strong>网关品牌（必填）：</strong> 文本输入框，用于输入网关品牌</li>
                        </ul>
                        <p class="implementation-note">实现要点：必填字段应进行非空验证，网关编码应检查唯一性</p>
                    </div>

                    <div class="description-section">
                        <h4>3. 配置信息区域</h4>
                        <p>配置信息区域包含以下字段：</p>
                        <ul>
                            <li><strong>所属空间：</strong> 下拉选择框，用于选择网关所属的空间</li>
                            <li><strong>网关协议（必填）：</strong> 下拉选择框，用于选择网关使用的通信协议</li>
                            <li><strong>状态：</strong> 开关控件，用于设置网关的启用/禁用状态</li>
                        </ul>
                        <p class="implementation-note">实现要点：空间列表和协议列表应从后端获取，支持动态更新</p>
                    </div>

                    <div class="description-section">
                        <h4>4. 其他信息区域</h4>
                        <p>其他信息区域包含以下字段：</p>
                        <ul>
                            <li><strong>网关描述：</strong> 文本区域，用于输入网关的详细描述信息</li>
                        </ul>
                        <p class="implementation-note">实现要点：文本区域应支持自动调整高度，提供字数限制提示</p>
                    </div>

                    <div class="description-section">
                        <h4>5. 表单验证</h4>
                        <p>表单验证的实现要求：</p>
                        <ul>
                            <li><strong>实时验证：</strong> 在用户输入过程中实时验证字段有效性</li>
                            <li><strong>提交验证：</strong> 在表单提交前进行完整性验证</li>
                            <li><strong>错误提示：</strong> 在字段下方显示明确的错误提示信息</li>
                            <li><strong>验证规则：</strong>
                                <ul>
                                    <li>网关名称：非空，长度限制（2-50字符）</li>
                                    <li>网关编码：非空，格式验证（字母、数字、连字符），唯一性检查</li>
                                    <li>网关品牌：非空</li>
                                    <li>网关协议：必须选择一项</li>
                                </ul>
                            </li>
                        </ul>
                        <p class="implementation-note">实现要点：唯一性验证应通过API异步检查，避免表单提交后才发现重复</p>
                    </div>

                    <div class="description-section">
                        <h4>6. 表单提交</h4>
                        <p>表单提交的处理流程：</p>
                        <ul>
                            <li><strong>提交触发：</strong> 点击顶部标题栏的保存按钮或表单底部的保存按钮</li>
                            <li><strong>提交前验证：</strong> 检查所有必填字段和验证规则</li>
                            <li><strong>提交状态：</strong> 显示加载状态，禁用提交按钮防止重复提交</li>
                            <li><strong>提交成功：</strong> 显示成功提示，并跳转回网关管理页面</li>
                            <li><strong>提交失败：</strong> 显示错误信息，保留用户输入的数据</li>
                        </ul>
                        <p class="implementation-note">实现要点：表单数据应序列化为JSON格式，通过POST请求发送到后端API</p>
                    </div>

                    <div class="description-section">
                        <h4>7. 取消操作</h4>
                        <p>取消操作的处理流程：</p>
                        <ul>
                            <li><strong>触发方式：</strong> 点击顶部标题栏的返回按钮或表单底部的取消按钮</li>
                            <li><strong>数据处理：</strong> 放弃当前输入的所有数据</li>
                            <li><strong>页面跳转：</strong> 返回到网关管理页面</li>
                            <li><strong>确认提示：</strong> 如果用户已输入数据，应显示确认对话框</li>
                        </ul>
                        <p class="implementation-note">实现要点：可以使用浏览器的beforeunload事件检测用户离开页面时是否有未保存数据</p>
                    </div>

                    <div class="description-section">
                        <h4>8. 用户体验优化</h4>
                        <p>表单页面的用户体验优化：</p>
                        <ul>
                            <li><strong>输入提示：</strong> 为每个字段提供示例和提示信息</li>
                            <li><strong>自动聚焦：</strong> 页面加载后自动聚焦到第一个输入字段</li>
                            <li><strong>键盘导航：</strong> 支持Tab键在字段间导航和Enter键提交表单</li>
                            <li><strong>表单分组：</strong> 将相关字段分组，提高表单结构清晰度</li>
                            <li><strong>响应式设计：</strong> 适应不同屏幕尺寸，确保在移动设备上可用</li>
                        </ul>
                        <p class="implementation-note">实现要点：表单控件应有明确的焦点状态，提高可访问性</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 设备配置 -->
        <div>
            <h2 class="screen-title">设备配置</h2>
            <div class="phone-frame">
                <iframe src="device.html"></iframe>
            </div>
        </div>

        <!-- 设备配置页面说明 -->
        <div>
            <h2 class="screen-title">设备配置页面说明</h2>
            <div class="login-description">
                <div class="description-header">
                    <h3>设备配置页面功能说明</h3>
                    <p class="description-subtitle">针对开发人员的实现指南</p>
                </div>

                <div class="description-content">
                    <div class="description-section">
                        <h4>1. 页面结构</h4>
                        <p>设备配置页面是设备管理的中心，包含以下主要部分：</p>
                        <ul>
                            <li><strong>顶部标题栏：</strong> 显示"设备配置"标题、返回按钮和搜索按钮</li>
                            <li><strong>搜索栏：</strong> 用于搜索设备</li>
                            <li><strong>分段控制器：</strong> 包含"设备管理"、"设备类型"和"设备分组"三个标签</li>
                            <li><strong>内容区域：</strong> 根据选中的标签显示不同内容</li>
                            <li><strong>浮动添加按钮：</strong> 用于添加新设备、设备类型或设备分组</li>
                        </ul>
                        <p class="implementation-note">实现要点：使用JavaScript实现标签切换功能，保持UI状态与当前选中的标签一致</p>
                    </div>

                    <div class="description-section">
                        <h4>2. 设备管理标签</h4>
                        <p>设备管理标签显示所有设备，按分组组织：</p>
                        <ul>
                            <li><strong>分组标题：</strong> 显示分组名称和设备数量</li>
                            <li><strong>设备网格：</strong> 以网格形式展示该分组下的设备</li>
                            <li><strong>设备卡片：</strong> 显示设备图标、名称、类型和状态</li>
                            <li><strong>状态指示器：</strong> 使用颜色和文字指示设备在线状态</li>
                        </ul>
                        <p class="implementation-note">实现要点：设备状态应实时更新，可使用WebSocket或定期轮询</p>
                    </div>

                    <div class="description-section">
                        <h4>3. 设备类型标签</h4>
                        <p>设备类型标签显示所有设备类型：</p>
                        <ul>
                            <li><strong>类型卡片：</strong> 显示类型图标、名称和设备数量</li>
                            <li><strong>视觉区分：</strong> 不同类型使用不同的图标和颜色</li>
                            <li><strong>操作按钮：</strong> 包含编辑和更多操作按钮</li>
                            <li><strong>点击行为：</strong> 点击卡片可查看该类型下的所有设备</li>
                        </ul>
                        <p class="implementation-note">实现要点：类型数据应从后端获取，支持自定义类型的添加和编辑</p>
                    </div>

                    <div class="description-section">
                        <h4>4. 设备分组标签</h4>
                        <p>设备分组标签显示所有设备分组：</p>
                        <ul>
                            <li><strong>分组卡片：</strong> 显示分组图标、名称和设备数量</li>
                            <li><strong>视觉区分：</strong> 不同分组使用不同的颜色</li>
                            <li><strong>操作按钮：</strong> 包含编辑和更多操作按钮</li>
                            <li><strong>点击行为：</strong> 点击卡片可查看该分组下的所有设备</li>
                        </ul>
                        <p class="implementation-note">实现要点：分组应支持层级结构，可以有父分组和子分组</p>
                    </div>

                    <div class="description-section">
                        <h4>5. 搜索功能</h4>
                        <p>搜索栏的功能实现：</p>
                        <ul>
                            <li><strong>搜索范围：</strong> 根据当前标签搜索设备、类型或分组</li>
                            <li><strong>搜索字段：</strong> 设备名称、ID、类型、状态等</li>
                            <li><strong>实时搜索：</strong> 输入时即时过滤结果</li>
                            <li><strong>搜索提示：</strong> 可显示搜索建议和历史记录</li>
                        </ul>
                        <p class="implementation-note">实现要点：搜索应支持模糊匹配和拼音搜索，提高用户搜索体验</p>
                    </div>

                    <div class="description-section">
                        <h4>6. 浮动添加按钮</h4>
                        <p>浮动添加按钮的功能实现：</p>
                        <ul>
                            <li><strong>位置：</strong> 固定在页面右下角，不随页面滚动而移动</li>
                            <li><strong>动画效果：</strong> 有轻微的脉动动画，提高可见性</li>
                            <li><strong>点击行为：</strong> 根据当前标签跳转到相应的添加页面</li>
                            <li><strong>权限控制：</strong> 根据用户权限决定是否显示此按钮</li>
                        </ul>
                        <p class="implementation-note">实现要点：按钮应有明显的点击反馈，且不应被其他元素遮挡</p>
                    </div>

                    <div class="description-section">
                        <h4>7. 设备操作</h4>
                        <p>设备卡片的操作功能：</p>
                        <ul>
                            <li><strong>点击行为：</strong> 点击设备卡片跳转到设备详情页面</li>
                            <li><strong>长按行为：</strong> 长按可进入多选模式，支持批量操作</li>
                            <li><strong>状态切换：</strong> 可直接在卡片上切换设备的开关状态</li>
                            <li><strong>快捷操作：</strong> 可添加常用操作的快捷按钮</li>
                        </ul>
                        <p class="implementation-note">实现要点：操作应有适当的确认机制，防止误操作</p>
                    </div>

                    <div class="description-section">
                        <h4>8. 类型和分组操作</h4>
                        <p>类型和分组卡片的操作功能：</p>
                        <ul>
                            <li><strong>编辑操作：</strong> 点击编辑按钮可修改类型或分组信息</li>
                            <li><strong>更多操作：</strong> 点击更多按钮显示删除、移动等操作</li>
                            <li><strong>拖拽排序：</strong> 支持拖拽调整类型或分组的顺序</li>
                            <li><strong>批量管理：</strong> 支持批量移动设备到不同类型或分组</li>
                        </ul>
                        <p class="implementation-note">实现要点：删除操作应检查是否有设备使用该类型或分组，并提供适当的提示</p>
                    </div>

                    <div class="description-section">
                        <h4>9. 数据同步与缓存</h4>
                        <p>数据处理策略：</p>
                        <ul>
                            <li><strong>初始加载：</strong> 页面加载时从服务器获取数据</li>
                            <li><strong>本地缓存：</strong> 缓存数据以提高性能和支持离线访问</li>
                            <li><strong>增量更新：</strong> 仅获取变更的数据，减少网络传输</li>
                            <li><strong>冲突解决：</strong> 处理多用户同时编辑导致的数据冲突</li>
                        </ul>
                        <p class="implementation-note">实现要点：使用IndexedDB或localStorage进行本地缓存，实现离线功能</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 规则配置 -->
        <div>
            <h2 class="screen-title">规则配置</h2>
            <div class="phone-frame">
                <iframe src="rules.html"></iframe>
            </div>
        </div>

        <!-- 规则配置页面说明 -->
        <div>
            <h2 class="screen-title">规则配置页面说明</h2>
            <div class="login-description">
                <div class="description-header">
                    <h3>规则配置页面功能说明</h3>
                    <p class="description-subtitle">针对开发人员的实现指南</p>
                </div>

                <div class="description-content">
                    <div class="description-section">
                        <h4>1. 页面结构</h4>
                        <p>规则配置页面用于管理自动化规则和查看规则执行日志，包含以下主要部分：</p>
                        <ul>
                            <li><strong>顶部标题栏：</strong> 显示"规则配置"标题和添加按钮</li>
                            <li><strong>搜索栏：</strong> 用于搜索规则</li>
                            <li><strong>分段控制器：</strong> 包含"规则管理"和"规则日志"两个标签</li>
                            <li><strong>规则列表：</strong> 显示所有已配置的规则</li>
                            <li><strong>底部导航栏：</strong> 提供主要功能区域的快速访问</li>
                        </ul>
                        <p class="implementation-note">实现要点：页面应支持下拉刷新，更新规则状态和列表</p>
                    </div>

                    <div class="description-section">
                        <h4>2. 规则卡片</h4>
                        <p>每个规则以卡片形式展示，包含以下元素：</p>
                        <ul>
                            <li><strong>规则标题：</strong> 显示规则名称</li>
                            <li><strong>启用开关：</strong> 用于启用或禁用规则</li>
                            <li><strong>规则描述：</strong> 简要说明规则的功能和触发条件</li>
                            <li><strong>规则信息：</strong> 显示创建时间、触发次数等元数据</li>
                            <li><strong>操作按钮：</strong> 提供查看历史记录和编辑规则的功能</li>
                        </ul>
                        <p class="implementation-note">实现要点：规则状态切换应实时同步到后端，并提供适当的视觉反馈</p>
                    </div>

                    <div class="description-section">
                        <h4>3. 规则管理标签</h4>
                        <p>规则管理标签的功能和交互：</p>
                        <ul>
                            <li><strong>规则列表：</strong> 显示所有已配置的规则</li>
                            <li><strong>排序选项：</strong> 支持按名称、创建时间、触发次数等排序</li>
                            <li><strong>筛选选项：</strong> 支持按规则类型、状态等筛选</li>
                            <li><strong>批量操作：</strong> 支持批量启用、禁用或删除规则</li>
                        </ul>
                        <p class="implementation-note">实现要点：列表应支持分页加载，避免一次加载过多数据影响性能</p>
                    </div>

                    <div class="description-section">
                        <h4>4. 规则日志标签</h4>
                        <p>规则日志标签的功能和内容：</p>
                        <ul>
                            <li><strong>日志筛选器：</strong> 提供多种筛选选项（全部、今天、昨天、本周、本月、成功、警告、错误）</li>
                            <li><strong>日志列表：</strong> 显示规则触发和执行的历史记录</li>
                            <li><strong>日志项：</strong> 包含规则名称、触发时间、执行内容和状态标签</li>
                            <li><strong>状态标签：</strong> 使用不同颜色区分不同状态（成功、警告、错误）</li>
                            <li><strong>详情链接：</strong> 点击"查看详情"可查看日志的完整信息</li>
                        </ul>
                        <p class="implementation-note">实现要点：日志应包含足够的上下文信息，便于问题排查和分析；状态应使用直观的颜色编码</p>
                    </div>

                    <div class="description-section">
                        <h4>5. 浮动添加按钮</h4>
                        <p>浮动添加按钮的功能实现：</p>
                        <ul>
                            <li><strong>位置：</strong> 固定在页面右下角，不随页面滚动而移动</li>
                            <li><strong>动画效果：</strong> 有轻微的脉动动画，提高可见性</li>
                            <li><strong>点击行为：</strong> 点击后跳转到添加规则页面</li>
                            <li><strong>权限控制：</strong> 根据用户权限决定是否显示此按钮</li>
                        </ul>
                        <p class="implementation-note">实现要点：按钮应有明显的点击反馈，且不应被其他元素遮挡</p>
                    </div>

                    <div class="description-section">
                        <h4>6. 添加规则功能</h4>
                        <p>添加新规则的流程和界面：</p>
                        <ul>
                            <li><strong>触发方式：</strong> 点击浮动添加按钮</li>
                            <li><strong>规则模板：</strong> 提供常用规则模板供用户选择</li>
                            <li><strong>自定义规则：</strong> 支持用户自定义规则的触发条件和执行动作</li>
                            <li><strong>规则验证：</strong> 在保存前验证规则的有效性</li>
                        </ul>
                        <p class="implementation-note">实现要点：规则编辑界面应提供可视化的条件和动作配置，降低用户学习成本</p>
                    </div>

                    <div class="description-section">
                        <h4>7. 规则编辑功能</h4>
                        <p>编辑现有规则的功能和界面：</p>
                        <ul>
                            <li><strong>触发方式：</strong> 点击规则卡片上的"编辑规则"按钮</li>
                            <li><strong>编辑界面：</strong> 与添加规则界面类似，但预填充现有规则数据</li>
                            <li><strong>版本控制：</strong> 支持查看和恢复规则的历史版本</li>
                            <li><strong>测试功能：</strong> 允许用户在保存前测试规则的执行效果</li>
                        </ul>
                        <p class="implementation-note">实现要点：编辑操作应实现乐观锁定，防止多用户同时编辑导致的数据覆盖</p>
                    </div>

                    <div class="description-section">
                        <h4>8. 规则执行机制</h4>
                        <p>规则的执行和监控机制：</p>
                        <ul>
                            <li><strong>触发条件：</strong> 支持多种触发条件，如设备状态变化、时间触发、数据阈值等</li>
                            <li><strong>执行动作：</strong> 支持多种执行动作，如发送通知、控制设备、记录数据等</li>
                            <li><strong>执行状态：</strong> 实时显示规则的执行状态和最近触发时间</li>
                            <li><strong>错误处理：</strong> 当规则执行失败时提供错误信息和重试机制</li>
                        </ul>
                        <p class="implementation-note">实现要点：规则引擎应支持分布式部署，确保高可用性和可扩展性</p>
                    </div>

                    <div class="description-section">
                        <h4>9. 搜索和筛选功能</h4>
                        <p>规则搜索和筛选的实现：</p>
                        <ul>
                            <li><strong>搜索字段：</strong> 支持按规则名称、描述、触发条件等搜索</li>
                            <li><strong>高级筛选：</strong> 提供多条件组合筛选，如规则类型、状态、创建时间等</li>
                            <li><strong>搜索历史：</strong> 记录用户最近的搜索关键词</li>
                            <li><strong>搜索建议：</strong> 根据输入提供智能搜索建议</li>
                        </ul>
                        <p class="implementation-note">实现要点：搜索应支持模糊匹配和拼音搜索，提高用户搜索体验</p>
                    </div>

                    <div class="description-section">
                        <h4>10. 权限控制</h4>
                        <p>规则配置的权限控制机制：</p>
                        <ul>
                            <li><strong>查看权限：</strong> 控制用户可以查看哪些规则</li>
                            <li><strong>编辑权限：</strong> 控制用户可以编辑哪些规则</li>
                            <li><strong>执行权限：</strong> 控制用户可以手动触发哪些规则</li>
                            <li><strong>审批流程：</strong> 重要规则的修改可能需要审批流程</li>
                        </ul>
                        <p class="implementation-note">实现要点：权限控制应细粒度到单个规则，并支持基于角色的权限分配</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 任务配置 -->
        <div>
            <h2 class="screen-title">任务配置</h2>
            <div class="phone-frame">
                <iframe src="tasks.html"></iframe>
            </div>
        </div>

        <!-- 任务配置页面说明 -->
        <div>
            <h2 class="screen-title">任务配置页面说明</h2>
            <div class="login-description">
                <div class="description-header">
                    <h3>任务配置页面功能说明</h3>
                    <p class="description-subtitle">针对开发人员的实现指南</p>
                </div>

                <div class="description-content">
                    <div class="description-section">
                        <h4>1. 页面结构</h4>
                        <p>任务配置页面用于管理自动化任务和查看任务执行日志，包含以下主要部分：</p>
                        <ul>
                            <li><strong>顶部标题栏：</strong> 显示"任务配置"标题、返回按钮和搜索按钮</li>
                            <li><strong>搜索栏：</strong> 用于搜索任务</li>
                            <li><strong>分段控制器：</strong> 包含"任务管理"和"任务日志"两个标签</li>
                            <li><strong>任务列表：</strong> 显示所有已配置的任务</li>
                            <li><strong>浮动添加按钮：</strong> 用于添加新任务</li>
                        </ul>
                        <p class="implementation-note">实现要点：页面应支持下拉刷新，更新任务状态和列表</p>
                    </div>

                    <div class="description-section">
                        <h4>2. 任务卡片</h4>
                        <p>每个任务以卡片形式展示，包含以下元素：</p>
                        <ul>
                            <li><strong>任务标题：</strong> 显示任务名称</li>
                            <li><strong>任务状态：</strong> 显示任务当前状态（运行中、已暂停、已完成等）</li>
                            <li><strong>任务描述：</strong> 简要说明任务的功能和执行条件</li>
                            <li><strong>任务信息：</strong> 显示执行频率、上次执行时间等元数据</li>
                            <li><strong>操作按钮：</strong> 提供暂停/启动、编辑等功能</li>
                        </ul>
                        <p class="implementation-note">实现要点：任务状态切换应实时同步到后端，并提供适当的视觉反馈</p>
                    </div>

                    <div class="description-section">
                        <h4>3. 任务管理标签</h4>
                        <p>任务管理标签的功能和交互：</p>
                        <ul>
                            <li><strong>任务列表：</strong> 显示所有已配置的任务</li>
                            <li><strong>排序选项：</strong> 支持按名称、创建时间、执行频率等排序</li>
                            <li><strong>筛选选项：</strong> 支持按任务类型、状态等筛选</li>
                            <li><strong>批量操作：</strong> 支持批量启用、禁用或删除任务</li>
                        </ul>
                        <p class="implementation-note">实现要点：列表应支持分页加载，避免一次加载过多数据影响性能</p>
                    </div>

                    <div class="description-section">
                        <h4>4. 任务日志标签</h4>
                        <p>任务日志标签的功能和内容：</p>
                        <ul>
                            <li><strong>日志筛选器：</strong> 提供多种筛选选项（全部、今天、昨天、本周、成功、警告、错误）</li>
                            <li><strong>日志列表：</strong> 显示任务执行的历史记录</li>
                            <li><strong>日志项：</strong> 包含任务名称、执行时间、执行结果和状态标签</li>
                            <li><strong>状态标签：</strong> 使用不同颜色区分不同状态（成功、警告、错误）</li>
                            <li><strong>详情链接：</strong> 点击"查看详情"可查看日志的完整信息</li>
                        </ul>
                        <p class="implementation-note">实现要点：日志应包含足够的上下文信息，便于问题排查和分析；状态应使用直观的颜色编码</p>
                    </div>

                    <div class="description-section">
                        <h4>5. 浮动添加按钮</h4>
                        <p>浮动添加按钮的功能实现：</p>
                        <ul>
                            <li><strong>位置：</strong> 固定在页面右下角，不随页面滚动而移动</li>
                            <li><strong>动画效果：</strong> 有轻微的脉动动画，提高可见性</li>
                            <li><strong>点击行为：</strong> 点击后跳转到添加任务页面</li>
                            <li><strong>权限控制：</strong> 根据用户权限决定是否显示此按钮</li>
                        </ul>
                        <p class="implementation-note">实现要点：按钮应有明显的点击反馈，且不应被其他元素遮挡</p>
                    </div>

                    <div class="description-section">
                        <h4>6. 添加任务功能</h4>
                        <p>添加新任务的流程和界面：</p>
                        <ul>
                            <li><strong>触发方式：</strong> 点击浮动添加按钮</li>
                            <li><strong>任务模板：</strong> 提供常用任务模板供用户选择</li>
                            <li><strong>自定义任务：</strong> 支持用户自定义任务的执行条件和操作</li>
                            <li><strong>定时设置：</strong> 支持设置任务的执行频率和时间</li>
                            <li><strong>任务验证：</strong> 在保存前验证任务的有效性</li>
                        </ul>
                        <p class="implementation-note">实现要点：任务编辑界面应提供友好的时间选择器和频率设置选项</p>
                    </div>

                    <div class="description-section">
                        <h4>7. 任务执行机制</h4>
                        <p>任务的执行和监控机制：</p>
                        <ul>
                            <li><strong>执行方式：</strong> 支持定时执行、条件触发和手动执行</li>
                            <li><strong>执行引擎：</strong> 后台任务调度系统，确保任务按时执行</li>
                            <li><strong>执行状态：</strong> 实时显示任务的执行状态和进度</li>
                            <li><strong>错误处理：</strong> 当任务执行失败时提供错误信息和重试机制</li>
                        </ul>
                        <p class="implementation-note">实现要点：任务执行应支持分布式部署，确保高可用性和可扩展性</p>
                    </div>

                    <div class="description-section">
                        <h4>8. 搜索和筛选功能</h4>
                        <p>任务搜索和筛选的实现：</p>
                        <ul>
                            <li><strong>搜索字段：</strong> 支持按任务名称、描述、类型等搜索</li>
                            <li><strong>高级筛选：</strong> 提供多条件组合筛选，如任务类型、状态、创建时间等</li>
                            <li><strong>搜索历史：</strong> 记录用户最近的搜索关键词</li>
                            <li><strong>搜索建议：</strong> 根据输入提供智能搜索建议</li>
                        </ul>
                        <p class="implementation-note">实现要点：搜索应支持模糊匹配和拼音搜索，提高用户搜索体验</p>
                    </div>

                    <div class="description-section">
                        <h4>9. 权限控制</h4>
                        <p>任务配置的权限控制机制：</p>
                        <ul>
                            <li><strong>查看权限：</strong> 控制用户可以查看哪些任务</li>
                            <li><strong>编辑权限：</strong> 控制用户可以编辑哪些任务</li>
                            <li><strong>执行权限：</strong> 控制用户可以手动触发哪些任务</li>
                            <li><strong>审批流程：</strong> 重要任务的修改可能需要审批流程</li>
                        </ul>
                        <p class="implementation-note">实现要点：权限控制应细粒度到单个任务，并支持基于角色的权限分配</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 空间配置 -->
        <div>
            <h2 class="screen-title">空间配置</h2>
            <div class="phone-frame">
                <iframe src="space.html"></iframe>
            </div>
        </div>

        <!-- 空间配置页面说明 -->
        <div>
            <h2 class="screen-title">空间配置页面说明</h2>
            <div class="login-description">
                <div class="description-header">
                    <h3>空间配置页面功能说明</h3>
                    <p class="description-subtitle">针对开发人员的实现指南</p>
                </div>

                <div class="description-content">
                    <div class="description-section">
                        <h4>1. 页面结构</h4>
                        <p>空间配置页面用于管理空间层级结构和空间类型，包含以下主要部分：</p>
                        <ul>
                            <li><strong>顶部标题栏：</strong> 显示"空间配置"标题、返回按钮和搜索按钮</li>
                            <li><strong>搜索栏：</strong> 用于搜索空间</li>
                            <li><strong>分段控制器：</strong> 包含"空间管理"和"空间类型"两个标签</li>
                            <li><strong>空间卡片：</strong> 显示空间的基本信息和统计数据</li>
                            <li><strong>空间层级：</strong> 以树形结构展示空间的层级关系</li>
                            <li><strong>浮动添加按钮：</strong> 用于添加新空间或空间类型</li>
                        </ul>
                        <p class="implementation-note">实现要点：页面应支持下拉刷新，更新空间数据和层级结构</p>
                    </div>

                    <div class="description-section">
                        <h4>2. 空间卡片</h4>
                        <p>空间卡片展示空间的详细信息，包含以下元素：</p>
                        <ul>
                            <li><strong>空间封面：</strong> 显示空间的背景图片</li>
                            <li><strong>空间名称：</strong> 显示空间的名称</li>
                            <li><strong>空间类型：</strong> 显示空间的类型（商业园区、工业园区等）</li>
                            <li><strong>统计数据：</strong> 显示建筑数量、楼层数量、房间数量、设备数量等统计信息</li>
                            <li><strong>操作按钮：</strong> 提供查看地图、编辑空间等功能</li>
                        </ul>
                        <p class="implementation-note">实现要点：卡片应支持点击展开查看更多详细信息，图片应支持懒加载以提高性能</p>
                    </div>

                    <div class="description-section">
                        <h4>3. 空间层级</h4>
                        <p>空间层级以树形结构展示空间的层级关系：</p>
                        <ul>
                            <li><strong>根节点：</strong> 顶层空间，如园区、校区等</li>
                            <li><strong>子节点：</strong> 下级空间，如建筑、楼层、房间等</li>
                            <li><strong>节点图标：</strong> 不同类型的空间使用不同的图标</li>
                            <li><strong>展开/折叠：</strong> 支持点击展开或折叠子节点</li>
                            <li><strong>节点操作：</strong> 支持添加、编辑、删除节点</li>
                        </ul>
                        <p class="implementation-note">实现要点：树形结构应支持懒加载，只在展开节点时加载其子节点，以提高性能</p>
                    </div>

                    <div class="description-section">
                        <h4>4. 空间管理标签</h4>
                        <p>空间管理标签的功能和交互：</p>
                        <ul>
                            <li><strong>空间列表：</strong> 显示所有空间卡片</li>
                            <li><strong>空间层级：</strong> 显示空间的层级结构</li>
                            <li><strong>排序选项：</strong> 支持按名称、创建时间、空间类型等排序</li>
                            <li><strong>筛选选项：</strong> 支持按空间类型、状态等筛选</li>
                        </ul>
                        <p class="implementation-note">实现要点：空间数据应支持分页加载，避免一次加载过多数据影响性能</p>
                    </div>

                    <div class="description-section">
                        <h4>5. 空间类型标签</h4>
                        <p>空间类型标签的功能和内容：</p>
                        <ul>
                            <li><strong>类型卡片：</strong> 显示空间类型的名称、图标和空间数量</li>
                            <li><strong>类型图标：</strong> 使用不同颜色和图标区分不同类型</li>
                            <li><strong>操作按钮：</strong> 提供编辑和更多操作功能</li>
                            <li><strong>点击行为：</strong> 点击类型卡片可查看该类型下的所有空间</li>
                        </ul>
                        <p class="implementation-note">实现要点：空间类型应支持自定义，包括名称、图标和颜色</p>
                    </div>

                    <div class="description-section">
                        <h4>6. 浮动添加按钮</h4>
                        <p>浮动添加按钮的功能实现：</p>
                        <ul>
                            <li><strong>位置：</strong> 固定在页面右下角，不随页面滚动而移动</li>
                            <li><strong>动画效果：</strong> 有轻微的脉动动画，提高可见性</li>
                            <li><strong>点击行为：</strong> 根据当前标签显示不同的添加选项（添加空间或添加空间类型）</li>
                            <li><strong>权限控制：</strong> 根据用户权限决定是否显示此按钮</li>
                        </ul>
                        <p class="implementation-note">实现要点：按钮应有明显的点击反馈，且不应被其他元素遮挡</p>
                    </div>

                    <div class="description-section">
                        <h4>7. 添加空间功能</h4>
                        <p>添加新空间的流程和界面：</p>
                        <ul>
                            <li><strong>触发方式：</strong> 点击浮动添加按钮或空间层级中的添加按钮</li>
                            <li><strong>基本信息：</strong> 包括空间名称、空间类型、上级空间等</li>
                            <li><strong>空间图片：</strong> 支持上传空间的背景图片</li>
                            <li><strong>空间属性：</strong> 支持设置空间的各种属性，如面积、容量等</li>
                            <li><strong>空间验证：</strong> 在保存前验证空间信息的完整性</li>
                        </ul>
                        <p class="implementation-note">实现要点：添加空间时应考虑层级关系，确保空间层级的正确性</p>
                    </div>

                    <div class="description-section">
                        <h4>8. 添加空间类型功能</h4>
                        <p>添加新空间类型的流程和界面：</p>
                        <ul>
                            <li><strong>触发方式：</strong> 在空间类型标签页点击浮动添加按钮</li>
                            <li><strong>类型信息：</strong> 包括类型名称、图标、颜色等</li>
                            <li><strong>属性定义：</strong> 支持定义该类型空间的默认属性</li>
                            <li><strong>类型验证：</strong> 在保存前验证类型信息的完整性</li>
                        </ul>
                        <p class="implementation-note">实现要点：空间类型应支持自定义图标和颜色，提供丰富的选择</p>
                    </div>

                    <div class="description-section">
                        <h4>9. 搜索和筛选功能</h4>
                        <p>空间搜索和筛选的实现：</p>
                        <ul>
                            <li><strong>搜索字段：</strong> 支持按空间名称、类型、属性等搜索</li>
                            <li><strong>高级筛选：</strong> 提供多条件组合筛选，如空间类型、创建时间等</li>
                            <li><strong>搜索历史：</strong> 记录用户最近的搜索关键词</li>
                            <li><strong>搜索建议：</strong> 根据输入提供智能搜索建议</li>
                        </ul>
                        <p class="implementation-note">实现要点：搜索应支持模糊匹配和拼音搜索，提高用户搜索体验</p>
                    </div>

                    <div class="description-section">
                        <h4>10. 权限控制</h4>
                        <p>空间配置的权限控制机制：</p>
                        <ul>
                            <li><strong>查看权限：</strong> 控制用户可以查看哪些空间</li>
                            <li><strong>编辑权限：</strong> 控制用户可以编辑哪些空间</li>
                            <li><strong>添加权限：</strong> 控制用户可以在哪些空间下添加子空间</li>
                            <li><strong>删除权限：</strong> 控制用户可以删除哪些空间</li>
                        </ul>
                        <p class="implementation-note">实现要点：权限控制应考虑空间的层级关系，上级空间的权限可能会影响下级空间</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 报警配置 -->
        <div>
            <h2 class="screen-title">报警配置</h2>
            <div class="phone-frame">
                <iframe src="alerts.html"></iframe>
            </div>
        </div>

        <!-- 报警配置页面说明 -->
        <div>
            <h2 class="screen-title">报警配置页面说明</h2>
            <div class="login-description">
                <div class="description-header">
                    <h3>报警配置页面功能说明</h3>
                    <p class="description-subtitle">针对开发人员的实现指南</p>
                </div>

                <div class="description-content">
                    <div class="description-section">
                        <h4>1. 页面结构</h4>
                        <p>报警配置页面用于管理报警规则、通知组和查看报警记录，包含以下主要部分：</p>
                        <ul>
                            <li><strong>顶部标题栏：</strong> 显示"报警配置"标题、返回按钮和搜索按钮</li>
                            <li><strong>搜索栏：</strong> 用于搜索报警</li>
                            <li><strong>分段控制器：</strong> 包含"通知组管理"、"报警规则"和"报警记录"三个标签</li>
                            <li><strong>标签内容区域：</strong> 根据选中的标签显示不同的内容</li>
                            <li><strong>浮动添加按钮：</strong> 用于添加新的通知组或报警规则</li>
                        </ul>
                        <p class="implementation-note">实现要点：页面应支持下拉刷新，更新报警数据和规则列表</p>
                    </div>

                    <div class="description-section">
                        <h4>2. 通知组管理标签</h4>
                        <p>通知组管理标签用于管理报警通知的接收组，包含以下功能：</p>
                        <ul>
                            <li><strong>通知组卡片：</strong> 显示通知组的基本信息</li>
                            <li><strong>成员管理：</strong> 显示通知组的成员，支持添加和删除成员</li>
                            <li><strong>通知渠道：</strong> 配置通知的发送渠道（短信、邮件、微信、电话等）</li>
                            <li><strong>启用/禁用：</strong> 通过开关控制通知组的启用状态</li>
                            <li><strong>编辑/删除：</strong> 提供编辑和删除通知组的功能</li>
                        </ul>
                        <p class="implementation-note">实现要点：通知组状态切换应实时同步到后端，并提供适当的视觉反馈</p>
                    </div>

                    <div class="description-section">
                        <h4>3. 报警规则标签</h4>
                        <p>报警规则标签用于管理报警触发条件和动作，包含以下功能：</p>
                        <ul>
                            <li><strong>规则卡片：</strong> 显示报警规则的基本信息</li>
                            <li><strong>规则描述：</strong> 简要说明规则的功能和用途</li>
                            <li><strong>触发条件：</strong> 显示触发报警的条件（如温度超过阈值、设备离线等）</li>
                            <li><strong>严重程度：</strong> 标识报警的严重程度（紧急、警告、信息等）</li>
                            <li><strong>通知组：</strong> 指定报警触发时要通知的组</li>
                            <li><strong>启用/禁用：</strong> 通过开关控制规则的启用状态</li>
                            <li><strong>编辑/历史：</strong> 提供编辑规则和查看历史记录的功能</li>
                        </ul>
                        <p class="implementation-note">实现要点：规则条件编辑应提供直观的界面，支持多种条件组合和逻辑关系</p>
                    </div>

                    <div class="description-section">
                        <h4>4. 报警记录标签</h4>
                        <p>报警记录标签用于查看和管理历史报警信息，包含以下功能：</p>
                        <ul>
                            <li><strong>筛选选项：</strong> 提供多种筛选选项（全部、紧急、警告、信息、已处理、未处理）</li>
                            <li><strong>报警卡片：</strong> 显示报警的详细信息</li>
                            <li><strong>报警图标：</strong> 使用不同颜色和图标区分不同类型的报警</li>
                            <li><strong>报警详情：</strong> 显示报警的设备、位置、状态等信息</li>
                            <li><strong>处理操作：</strong> 提供标记已处理、创建工单等功能</li>
                        </ul>
                        <p class="implementation-note">实现要点：报警记录应支持分页加载和实时更新，确保用户能及时看到最新的报警信息</p>
                    </div>

                    <div class="description-section">
                        <h4>5. 浮动添加按钮</h4>
                        <p>浮动添加按钮的功能实现：</p>
                        <ul>
                            <li><strong>位置：</strong> 固定在页面右下角，不随页面滚动而移动</li>
                            <li><strong>动画效果：</strong> 有轻微的脉动动画，提高可见性</li>
                            <li><strong>点击行为：</strong> 根据当前标签显示不同的添加选项（添加通知组或添加报警规则）</li>
                            <li><strong>权限控制：</strong> 根据用户权限决定是否显示此按钮</li>
                        </ul>
                        <p class="implementation-note">实现要点：按钮应有明显的点击反馈，且不应被其他元素遮挡</p>
                    </div>

                    <div class="description-section">
                        <h4>6. 添加通知组功能</h4>
                        <p>添加新通知组的流程和界面：</p>
                        <ul>
                            <li><strong>触发方式：</strong> 在通知组管理标签页点击浮动添加按钮</li>
                            <li><strong>基本信息：</strong> 设置通知组名称和描述</li>
                            <li><strong>成员选择：</strong> 从用户列表中选择通知组成员</li>
                            <li><strong>通知渠道：</strong> 选择通知的发送渠道（短信、邮件、微信、电话等）</li>
                            <li><strong>通知时段：</strong> 设置允许发送通知的时间段</li>
                        </ul>
                        <p class="implementation-note">实现要点：成员选择应支持搜索和批量添加，提高操作效率</p>
                    </div>

                    <div class="description-section">
                        <h4>7. 添加报警规则功能</h4>
                        <p>添加新报警规则的流程和界面：</p>
                        <ul>
                            <li><strong>触发方式：</strong> 在报警规则标签页点击浮动添加按钮</li>
                            <li><strong>基本信息：</strong> 设置规则名称和描述</li>
                            <li><strong>设备选择：</strong> 选择要监控的设备或设备组</li>
                            <li><strong>触发条件：</strong> 设置触发报警的条件（如温度超过阈值、设备离线等）</li>
                            <li><strong>严重程度：</strong> 设置报警的严重程度（紧急、警告、信息等）</li>
                            <li><strong>通知组：</strong> 选择报警触发时要通知的组</li>
                            <li><strong>动作配置：</strong> 设置报警触发时要执行的动作（如发送通知、创建工单等）</li>
                        </ul>
                        <p class="implementation-note">实现要点：条件设置应提供可视化的界面，支持多种条件组合和逻辑关系</p>
                    </div>

                    <div class="description-section">
                        <h4>8. 报警处理流程</h4>
                        <p>报警触发后的处理流程：</p>
                        <ul>
                            <li><strong>报警触发：</strong> 当满足触发条件时，系统生成报警记录</li>
                            <li><strong>通知发送：</strong> 根据规则配置，向指定的通知组发送通知</li>
                            <li><strong>报警确认：</strong> 用户可以确认收到报警通知</li>
                            <li><strong>报警处理：</strong> 用户可以标记报警为已处理，或创建工单进行处理</li>
                            <li><strong>处理记录：</strong> 系统记录报警的处理过程和结果</li>
                        </ul>
                        <p class="implementation-note">实现要点：报警处理状态应实时同步，确保多用户协作时不会出现冲突</p>
                    </div>

                    <div class="description-section">
                        <h4>9. 搜索和筛选功能</h4>
                        <p>报警搜索和筛选的实现：</p>
                        <ul>
                            <li><strong>搜索字段：</strong> 支持按报警名称、设备、位置等搜索</li>
                            <li><strong>高级筛选：</strong> 提供多条件组合筛选，如报警类型、严重程度、时间范围等</li>
                            <li><strong>搜索历史：</strong> 记录用户最近的搜索关键词</li>
                            <li><strong>搜索建议：</strong> 根据输入提供智能搜索建议</li>
                        </ul>
                        <p class="implementation-note">实现要点：搜索应支持模糊匹配和拼音搜索，提高用户搜索体验</p>
                    </div>

                    <div class="description-section">
                        <h4>10. 权限控制</h4>
                        <p>报警配置的权限控制机制：</p>
                        <ul>
                            <li><strong>查看权限：</strong> 控制用户可以查看哪些报警和规则</li>
                            <li><strong>编辑权限：</strong> 控制用户可以编辑哪些通知组和规则</li>
                            <li><strong>处理权限：</strong> 控制用户可以处理哪些报警</li>
                            <li><strong>审批流程：</strong> 重要规则的修改可能需要审批流程</li>
                        </ul>
                        <p class="implementation-note">实现要点：权限控制应细粒度到单个规则和通知组，并支持基于角色的权限分配</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 工单配置 -->
        <div>
            <h2 class="screen-title">工单配置</h2>
            <div class="phone-frame">
                <iframe src="workorders.html"></iframe>
            </div>
        </div>

        <!-- 工单配置页面说明 -->
        <div>
            <h2 class="screen-title">工单配置页面说明</h2>
            <div class="login-description">
                <div class="description-header">
                    <h3>工单配置页面功能说明</h3>
                    <p class="description-subtitle">针对开发人员的实现指南</p>
                </div>

                <div class="description-content">
                    <div class="description-section">
                        <h4>1. 页面结构</h4>
                        <p>工单配置页面用于管理维修、维护和安装工单，包含以下主要部分：</p>
                        <ul>
                            <li><strong>顶部标题栏：</strong> 显示"工单配置"标题、返回按钮和搜索按钮</li>
                            <li><strong>搜索栏：</strong> 用于搜索工单</li>
                            <li><strong>筛选标签：</strong> 包含全部、待处理、处理中、已完成等筛选选项</li>
                            <li><strong>工单列表：</strong> 显示符合筛选条件的工单</li>
                            <li><strong>浮动添加按钮：</strong> 用于创建新工单</li>
                        </ul>
                        <p class="implementation-note">实现要点：页面应支持下拉刷新，更新工单状态和列表</p>
                    </div>

                    <div class="description-section">
                        <h4>2. 工单卡片</h4>
                        <p>工单卡片展示工单的详细信息，包含以下元素：</p>
                        <ul>
                            <li><strong>工单标题：</strong> 显示工单名称和工单号</li>
                            <li><strong>工单来源：</strong> 标识工单是由系统用户创建还是由报警自动生成</li>
                            <li><strong>工单状态：</strong> 显示工单当前状态（待处理、处理中、已完成）</li>
                            <li><strong>工单描述：</strong> 简要说明工单的内容和目的</li>
                            <li><strong>工单详情：</strong> 显示设备、位置、创建时间、优先级等信息</li>
                            <li><strong>故障图片：</strong> 显示工单相关的图片</li>
                            <li><strong>操作按钮：</strong> 根据工单状态提供不同的操作选项</li>
                        </ul>
                        <p class="implementation-note">实现要点：工单卡片应根据工单状态显示不同的操作按钮，并使用不同颜色区分工单状态</p>
                    </div>

                    <div class="description-section">
                        <h4>3. 工单来源</h4>
                        <p>工单来源标识工单的创建方式，包括两种类型：</p>
                        <ul>
                            <li><strong>用户创建：</strong> 由系统用户手动创建的工单</li>
                            <li><strong>报警生成：</strong> 由系统报警自动生成的工单</li>
                        </ul>
                        <p class="implementation-note">实现要点：不同来源的工单应有不同的标识，便于用户快速识别</p>
                    </div>

                    <div class="description-section">
                        <h4>4. 待处理工单</h4>
                        <p>待处理工单标签显示所有待处理的工单，包含以下功能：</p>
                        <ul>
                            <li><strong>工单列表：</strong> 显示所有待处理的工单</li>
                            <li><strong>分配按钮：</strong> 用于将工单分配给指定的处理人员（仅对有分配权限的用户显示）</li>
                            <li><strong>开始处理按钮：</strong> 用于开始处理工单，将工单状态更改为"处理中"</li>
                        </ul>
                        <p class="implementation-note">实现要点：分配按钮应根据用户权限动态显示或隐藏</p>
                    </div>

                    <div class="description-section">
                        <h4>5. 处理中工单</h4>
                        <p>处理中工单标签显示所有正在处理的工单，包含以下功能：</p>
                        <ul>
                            <li><strong>工单列表：</strong> 显示所有处理中的工单</li>
                            <li><strong>添加备注按钮：</strong> 用于添加处理过程中的备注信息</li>
                            <li><strong>完成工单按钮：</strong> 用于标记工单为已完成</li>
                            <li><strong>处理人信息：</strong> 显示当前处理人的姓名</li>
                        </ul>
                        <p class="implementation-note">实现要点：工单状态变更应记录操作时间和操作人</p>
                    </div>

                    <div class="description-section">
                        <h4>6. 已完成工单</h4>
                        <p>已完成工单标签显示所有已完成的工单，包含以下功能：</p>
                        <ul>
                            <li><strong>工单列表：</strong> 显示所有已完成的工单</li>
                            <li><strong>查看报告按钮：</strong> 用于查看工单的完成报告</li>
                            <li><strong>复制工单按钮：</strong> 用于基于现有工单创建新工单</li>
                            <li><strong>完成时间：</strong> 显示工单的完成时间</li>
                        </ul>
                        <p class="implementation-note">实现要点：已完成工单应保留完整的处理记录，便于后续查询和分析</p>
                    </div>

                    <div class="description-section">
                        <h4>7. 新建工单</h4>
                        <p>新建工单功能允许用户创建新的工单，包含以下字段：</p>
                        <ul>
                            <li><strong>所属空间：</strong> 下拉选择，可以是无，非必选</li>
                            <li><strong>故障设备：</strong> 下拉选择，必填</li>
                            <li><strong>故障点位：</strong> 下拉选择，非必填</li>
                            <li><strong>故障描述：</strong> 文本区域，用于详细描述故障情况</li>
                            <li><strong>故障图片：</strong> 必填，最多上传5张图片，每张图片大小不超过10M</li>
                        </ul>
                        <p class="implementation-note">实现要点：图片上传应支持预览和删除，并进行大小和数量限制</p>
                    </div>

                    <div class="description-section">
                        <h4>8. 工单分配</h4>
                        <p>工单分配功能允许有权限的用户将工单分配给指定的处理人员：</p>
                        <ul>
                            <li><strong>权限控制：</strong> 只有具有分配工单权限的用户才能看到和使用分配按钮</li>
                            <li><strong>分配界面：</strong> 点击分配按钮后显示可选的处理人员列表</li>
                            <li><strong>通知机制：</strong> 工单分配后自动通知被分配的处理人员</li>
                            <li><strong>分配记录：</strong> 记录工单的分配历史，包括分配时间和分配人</li>
                        </ul>
                        <p class="implementation-note">实现要点：分配功能应考虑处理人员的工作负载，避免工作分配不均</p>
                    </div>

                    <div class="description-section">
                        <h4>9. 工单处理流程</h4>
                        <p>工单的完整处理流程包括以下步骤：</p>
                        <ul>
                            <li><strong>创建工单：</strong> 由用户手动创建或系统报警自动生成</li>
                            <li><strong>工单分配：</strong> 将工单分配给指定的处理人员（可选）</li>
                            <li><strong>开始处理：</strong> 处理人员开始处理工单，状态变为"处理中"</li>
                            <li><strong>处理过程：</strong> 处理人员可以添加处理过程中的备注和图片</li>
                            <li><strong>完成工单：</strong> 处理完成后，标记工单为已完成，并填写完成报告</li>
                            <li><strong>工单评价：</strong> 工单创建人可以对处理结果进行评价（可选）</li>
                        </ul>
                        <p class="implementation-note">实现要点：工单处理的每个步骤都应记录操作时间和操作人，形成完整的处理记录</p>
                    </div>

                    <div class="description-section">
                        <h4>10. 搜索和筛选</h4>
                        <p>工单搜索和筛选功能：</p>
                        <ul>
                            <li><strong>搜索字段：</strong> 支持按工单号、工单名称、设备名称、位置等搜索</li>
                            <li><strong>状态筛选：</strong> 支持按工单状态（待处理、处理中、已完成）筛选</li>
                            <li><strong>类型筛选：</strong> 支持按工单类型（维修、维护、安装）筛选</li>
                            <li><strong>时间筛选：</strong> 支持按创建时间、完成时间等筛选</li>
                            <li><strong>来源筛选：</strong> 支持按工单来源（用户创建、报警生成）筛选</li>
                        </ul>
                        <p class="implementation-note">实现要点：搜索和筛选应支持组合条件，提高查询效率</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 组态管理 -->
        <div>
            <h2 class="screen-title">组态管理</h2>
            <div class="phone-frame">
                <iframe src="scada.html"></iframe>
            </div>
        </div>

        <!-- 组态管理页面说明 -->
        <div>
            <h2 class="screen-title">组态管理页面说明</h2>
            <div class="login-description">
                <div class="description-header">
                    <h3>组态管理页面功能说明</h3>
                    <p class="description-subtitle">针对开发人员的实现指南</p>
                </div>

                <div class="description-content">
                    <div class="description-section">
                        <h4>1. 页面结构</h4>
                        <p>组态管理页面用于创建、编辑和管理可视化监控大屏，包含以下主要部分：</p>
                        <ul>
                            <li><strong>顶部标题栏：</strong> 显示"组态管理"标题和添加按钮</li>
                            <li><strong>搜索栏：</strong> 用于搜索组态项目</li>
                            <li><strong>最近项目：</strong> 显示用户最近访问或编辑的组态项目</li>
                            <li><strong>组件库：</strong> 提供各种可用于组态设计的组件</li>
                            <li><strong>模板库：</strong> 提供预设的组态模板</li>
                        </ul>
                        <p class="implementation-note">实现要点：页面应支持下拉刷新，更新项目列表和组件库</p>
                    </div>

                    <div class="description-section">
                        <h4>2. 组态项目卡片</h4>
                        <p>组态项目卡片展示组态项目的详细信息，包含以下元素：</p>
                        <ul>
                            <li><strong>项目预览图：</strong> 显示组态大屏的缩略图</li>
                            <li><strong>项目名称：</strong> 显示组态项目的名称</li>
                            <li><strong>项目类型：</strong> 标识项目的类型（监控大屏、数据分析等）</li>
                            <li><strong>项目描述：</strong> 简要说明项目的功能和用途</li>
                            <li><strong>更新时间：</strong> 显示项目的最近更新时间</li>
                            <li><strong>组件数量：</strong> 显示项目中使用的组件数量</li>
                            <li><strong>操作按钮：</strong> 提供预览和编辑功能</li>
                        </ul>
                        <p class="implementation-note">实现要点：项目预览图应支持懒加载，提高页面加载速度</p>
                    </div>

                    <div class="description-section">
                        <h4>3. 组件库</h4>
                        <p>组件库提供各种可用于组态设计的组件，包括：</p>
                        <ul>
                            <li><strong>图表组件：</strong> 折线图、柱状图、饼图等数据可视化组件</li>
                            <li><strong>表格组件：</strong> 用于展示结构化数据</li>
                            <li><strong>地图组件：</strong> 用于展示地理位置相关的数据</li>
                            <li><strong>仪表盘组件：</strong> 用于展示关键指标</li>
                            <li><strong>列表组件：</strong> 用于展示列表数据</li>
                            <li><strong>图片组件：</strong> 用于展示图片和图像</li>
                            <li><strong>自定义组件：</strong> 支持用户自定义组件</li>
                        </ul>
                        <p class="implementation-note">实现要点：组件应支持拖拽操作，便于在设计器中使用</p>
                    </div>

                    <div class="description-section">
                        <h4>4. 模板库</h4>
                        <p>模板库提供预设的组态模板，便于快速创建组态项目：</p>
                        <ul>
                            <li><strong>设备监控模板：</strong> 适用于设备状态监控大屏</li>
                            <li><strong>能源分析模板：</strong> 适用于能源消耗分析大屏</li>
                            <li><strong>安防监控模板：</strong> 适用于安防监控大屏</li>
                            <li><strong>环境监测模板：</strong> 适用于环境数据监测大屏</li>
                        </ul>
                        <p class="implementation-note">实现要点：模板应支持参数化配置，便于根据实际需求进行调整</p>
                    </div>

                    <div class="description-section">
                        <h4>5. 组态设计器</h4>
                        <p>组态设计器是创建和编辑组态项目的核心工具，包含以下功能：</p>
                        <ul>
                            <li><strong>画布：</strong> 提供组态设计的主要工作区域</li>
                            <li><strong>组件面板：</strong> 显示可用的组件列表</li>
                            <li><strong>属性面板：</strong> 用于配置选中组件的属性</li>
                            <li><strong>数据源配置：</strong> 用于配置组件的数据源</li>
                            <li><strong>事件配置：</strong> 用于配置组件的交互事件</li>
                            <li><strong>布局工具：</strong> 提供对齐、分布等布局功能</li>
                            <li><strong>预览功能：</strong> 支持实时预览组态效果</li>
                        </ul>
                        <p class="implementation-note">实现要点：设计器应支持响应式设计，确保组态在不同设备上的适配性</p>
                    </div>

                    <div class="description-section">
                        <h4>6. 数据源管理</h4>
                        <p>数据源管理用于配置组态项目的数据来源：</p>
                        <ul>
                            <li><strong>静态数据：</strong> 支持配置静态数据</li>
                            <li><strong>API接口：</strong> 支持配置REST API数据源</li>
                            <li><strong>数据库：</strong> 支持配置数据库数据源</li>
                            <li><strong>实时数据：</strong> 支持配置WebSocket等实时数据源</li>
                            <li><strong>数据转换：</strong> 支持对数据进行转换和处理</li>
                            <li><strong>数据刷新：</strong> 支持配置数据的自动刷新策略</li>
                        </ul>
                        <p class="implementation-note">实现要点：数据源配置应支持参数化，便于在不同环境中使用</p>
                    </div>

                    <div class="description-section">
                        <h4>7. 交互事件配置</h4>
                        <p>交互事件配置用于设置组件之间的交互关系：</p>
                        <ul>
                            <li><strong>点击事件：</strong> 配置组件点击时的行为</li>
                            <li><strong>悬停事件：</strong> 配置鼠标悬停时的行为</li>
                            <li><strong>数据联动：</strong> 配置组件之间的数据联动</li>
                            <li><strong>条件触发：</strong> 配置基于条件的事件触发</li>
                            <li><strong>动画效果：</strong> 配置组件的动画效果</li>
                            <li><strong>页面跳转：</strong> 配置页面跳转行为</li>
                        </ul>
                        <p class="implementation-note">实现要点：事件配置应支持可视化编辑，降低用户的学习成本</p>
                    </div>

                    <div class="description-section">
                        <h4>8. 组态发布与部署</h4>
                        <p>组态发布与部署用于将设计好的组态项目发布到生产环境：</p>
                        <ul>
                            <li><strong>版本管理：</strong> 支持组态项目的版本控制</li>
                            <li><strong>环境配置：</strong> 支持不同环境的配置管理</li>
                            <li><strong>权限控制：</strong> 支持设置组态项目的访问权限</li>
                            <li><strong>一键发布：</strong> 支持一键发布组态项目</li>
                            <li><strong>回滚功能：</strong> 支持版本回滚</li>
                            <li><strong>部署日志：</strong> 记录部署历史和状态</li>
                        </ul>
                        <p class="implementation-note">实现要点：发布过程应支持自动化测试，确保组态项目的质量</p>
                    </div>

                    <div class="description-section">
                        <h4>9. 组态运行时</h4>
                        <p>组态运行时是组态项目的执行环境：</p>
                        <ul>
                            <li><strong>数据加载：</strong> 负责从数据源加载数据</li>
                            <li><strong>组件渲染：</strong> 负责渲染组态组件</li>
                            <li><strong>事件处理：</strong> 负责处理交互事件</li>
                            <li><strong>数据刷新：</strong> 负责定期刷新数据</li>
                            <li><strong>性能优化：</strong> 负责优化组态的运行性能</li>
                            <li><strong>错误处理：</strong> 负责处理运行时错误</li>
                        </ul>
                        <p class="implementation-note">实现要点：运行时应支持离线缓存，提高在弱网环境下的可用性</p>
                    </div>

                    <div class="description-section">
                        <h4>10. 权限控制</h4>
                        <p>组态管理的权限控制机制：</p>
                        <ul>
                            <li><strong>查看权限：</strong> 控制用户可以查看哪些组态项目</li>
                            <li><strong>编辑权限：</strong> 控制用户可以编辑哪些组态项目</li>
                            <li><strong>发布权限：</strong> 控制用户可以发布哪些组态项目</li>
                            <li><strong>组件权限：</strong> 控制用户可以使用哪些组件</li>
                            <li><strong>数据源权限：</strong> 控制用户可以访问哪些数据源</li>
                            <li><strong>审批流程：</strong> 重要操作可能需要审批流程</li>
                        </ul>
                        <p class="implementation-note">实现要点：权限控制应细粒度到单个组态项目和组件，并支持基于角色的权限分配</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 巡更管理 -->
        <div>
            <h2 class="screen-title">巡更管理</h2>
            <div class="phone-frame">
                <iframe src="patrol.html"></iframe>
            </div>
        </div>

        <!-- 巡更管理页面说明 -->
        <div>
            <h2 class="screen-title">巡更管理页面说明</h2>
            <div class="login-description">
                <div class="description-header">
                    <h3>巡更管理页面功能说明</h3>
                    <p class="description-subtitle">针对开发人员的实现指南</p>
                </div>

                <div class="description-content">
                    <div class="description-section">
                        <h4>1. 页面结构</h4>
                        <p>巡更管理页面用于创建、管理和查看巡更任务、路线和记录，包含以下主要部分：</p>
                        <ul>
                            <li><strong>顶部标题栏：</strong> 显示"巡更管理"标题和添加按钮</li>
                            <li><strong>搜索栏：</strong> 用于搜索巡更任务、路线或记录</li>
                            <li><strong>分段控制器：</strong> 用于切换巡更任务、巡更路线和巡更记录三个标签</li>
                            <li><strong>筛选标签：</strong> 用于筛选不同状态或类型的巡更内容</li>
                            <li><strong>内容区域：</strong> 显示巡更任务、路线或记录的列表</li>
                            <li><strong>浮动添加按钮：</strong> 用于快速添加新的巡更任务或路线</li>
                        </ul>
                        <p class="implementation-note">实现要点：分段控制器切换时应保持用户的筛选条件，提高用户体验</p>
                    </div>

                    <div class="description-section">
                        <h4>2. 巡更任务管理</h4>
                        <p>巡更任务管理用于创建和管理巡更任务，包含以下功能：</p>
                        <ul>
                            <li><strong>任务列表：</strong> 显示所有巡更任务，包括任务名称、时间、状态等信息</li>
                            <li><strong>任务筛选：</strong> 支持按全部、待执行、进行中、已完成、已逾期等状态筛选任务</li>
                            <li><strong>任务详情：</strong> 显示任务的详细信息，包括巡检人、检查点数量等</li>
                            <li><strong>进度显示：</strong> 显示任务的完成进度，包括进度条和已完成检查点列表</li>
                            <li><strong>任务操作：</strong> 提供查看详情、导航路线、更换人员、编辑任务等操作</li>
                            <li><strong>创建任务：</strong> 支持创建新的巡更任务，指定路线、巡检人和时间等</li>
                        </ul>
                        <p class="implementation-note">实现要点：任务状态应实时更新，并支持推送通知提醒相关人员</p>
                    </div>

                    <div class="description-section">
                        <h4>3. 巡更路线管理</h4>
                        <p>巡更路线管理用于创建和管理巡更路线，包含以下功能：</p>
                        <ul>
                            <li><strong>路线列表：</strong> 显示所有巡更路线，包括路线名称、类型、检查点数量等信息</li>
                            <li><strong>路线筛选：</strong> 支持按全部、常规路线、临时路线、已停用等类型筛选路线</li>
                            <li><strong>路线详情：</strong> 显示路线的详细信息，包括路线描述、地图和检查点列表</li>
                            <li><strong>路线地图：</strong> 显示路线的地图视图，标记检查点的位置和顺序</li>
                            <li><strong>检查点管理：</strong> 支持添加、编辑和删除路线上的检查点</li>
                            <li><strong>路线操作：</strong> 提供查看记录、编辑路线、删除路线、启用/停用路线等操作</li>
                            <li><strong>创建路线：</strong> 支持创建新的巡更路线，设置路线类型、检查点和描述等</li>
                        </ul>
                        <p class="implementation-note">实现要点：路线地图应支持交互式编辑，方便用户调整检查点位置和顺序</p>
                    </div>

                    <div class="description-section">
                        <h4>4. 巡更记录管理</h4>
                        <p>巡更记录管理用于查看和分析巡更执行记录，包含以下功能：</p>
                        <ul>
                            <li><strong>记录列表：</strong> 显示所有巡更记录，包括记录名称、时间、状态等信息</li>
                            <li><strong>记录筛选：</strong> 支持按全部、正常、异常、今日、本周等条件筛选记录</li>
                            <li><strong>记录详情：</strong> 显示记录的详细信息，包括巡检人、路线、耗时等</li>
                            <li><strong>记录统计：</strong> 显示记录的统计信息，包括计划检查点、实际检查点、正常点位、异常点位和完成率等</li>
                            <li><strong>异常处理：</strong> 支持查看和处理巡检过程中发现的异常情况</li>
                            <li><strong>记录操作：</strong> 提供查看报告、分享、查看异常等操作</li>
                            <li><strong>数据导出：</strong> 支持导出巡更记录数据，用于进一步分析和报表生成</li>
                        </ul>
                        <p class="implementation-note">实现要点：记录应支持多种格式的导出，如PDF、Excel等，并支持自定义报表模板</p>
                    </div>

                    <div class="description-section">
                        <h4>5. 检查点管理</h4>
                        <p>检查点管理是巡更路线的核心组成部分，包含以下功能：</p>
                        <ul>
                            <li><strong>检查点类型：</strong> 支持多种检查点类型，如设备检查、安全检查、环境检查等</li>
                            <li><strong>检查项设置：</strong> 支持为每个检查点设置具体的检查项目和标准</li>
                            <li><strong>NFC/二维码：</strong> 支持通过NFC标签或二维码标识检查点，确保巡检人员到达现场</li>
                            <li><strong>位置验证：</strong> 支持通过GPS或室内定位技术验证巡检人员的位置</li>
                            <li><strong>检查记录：</strong> 支持记录检查结果，包括文字描述、选择项和图片等</li>
                            <li><strong>异常标记：</strong> 支持标记检查过程中发现的异常情况，并上报处理</li>
                            <li><strong>时间控制：</strong> 支持设置检查点的计划检查时间和最大停留时间</li>
                        </ul>
                        <p class="implementation-note">实现要点：检查点应支持离线操作，在网络不稳定的环境下也能正常工作</p>
                    </div>

                    <div class="description-section">
                        <h4>6. 巡检人员管理</h4>
                        <p>巡检人员管理用于管理执行巡更任务的人员，包含以下功能：</p>
                        <ul>
                            <li><strong>人员列表：</strong> 显示所有可执行巡更任务的人员</li>
                            <li><strong>人员分组：</strong> 支持将人员分组，便于任务分配</li>
                            <li><strong>能力标签：</strong> 支持为人员添加能力标签，如电气检查、安防检查等</li>
                            <li><strong>任务分配：</strong> 支持将巡更任务分配给合适的人员</li>
                            <li><strong>工作量统计：</strong> 统计人员的工作量，避免任务分配不均</li>
                            <li><strong>绩效评估：</strong> 基于巡检记录评估人员的工作绩效</li>
                            <li><strong>实时位置：</strong> 显示巡检人员的实时位置，便于调度和管理</li>
                        </ul>
                        <p class="implementation-note">实现要点：人员管理应考虑隐私保护，只在必要时获取和显示位置信息</p>
                    </div>

                    <div class="description-section">
                        <h4>7. 异常处理流程</h4>
                        <p>异常处理流程用于处理巡检过程中发现的异常情况，包含以下步骤：</p>
                        <ul>
                            <li><strong>异常发现：</strong> 巡检人员在巡检过程中发现异常情况</li>
                            <li><strong>异常记录：</strong> 记录异常的详细信息，包括文字描述、图片和视频等</li>
                            <li><strong>异常分类：</strong> 对异常进行分类，如设备故障、安全隐患、环境问题等</li>
                            <li><strong>异常上报：</strong> 将异常情况上报给相关负责人</li>
                            <li><strong>处理分配：</strong> 将异常处理任务分配给合适的人员</li>
                            <li><strong>处理跟踪：</strong> 跟踪异常处理的进度和结果</li>
                            <li><strong>处理确认：</strong> 确认异常已处理完成，并记录处理结果</li>
                            <li><strong>异常统计：</strong> 统计分析异常情况，发现潜在问题</li>
                        </ul>
                        <p class="implementation-note">实现要点：异常处理应支持优先级设置，确保重要异常得到及时处理</p>
                    </div>

                    <div class="description-section">
                        <h4>8. 数据分析与报表</h4>
                        <p>数据分析与报表用于分析巡更数据并生成报表，包含以下功能：</p>
                        <ul>
                            <li><strong>巡检统计：</strong> 统计巡检任务的完成情况、异常情况等</li>
                            <li><strong>趋势分析：</strong> 分析巡检数据的变化趋势，发现潜在问题</li>
                            <li><strong>热点分析：</strong> 分析异常多发区域和设备，进行重点关注</li>
                            <li><strong>效率分析：</strong> 分析巡检效率，优化巡检路线和任务分配</li>
                            <li><strong>报表生成：</strong> 生成各类巡检报表，如日报、周报、月报等</li>
                            <li><strong>报表模板：</strong> 支持自定义报表模板，满足不同需求</li>
                            <li><strong>报表导出：</strong> 支持将报表导出为PDF、Excel等格式</li>
                            <li><strong>报表分享：</strong> 支持将报表分享给相关人员</li>
                        </ul>
                        <p class="implementation-note">实现要点：数据分析应支持可视化展示，便于用户理解和决策</p>
                    </div>

                    <div class="description-section">
                        <h4>9. 移动端适配</h4>
                        <p>移动端适配确保巡更管理系统在移动设备上的良好体验，包含以下方面：</p>
                        <ul>
                            <li><strong>响应式设计：</strong> 确保页面在不同尺寸的设备上都能正常显示</li>
                            <li><strong>触摸优化：</strong> 优化界面元素的大小和间距，适合触摸操作</li>
                            <li><strong>离线支持：</strong> 支持在网络不稳定的环境下离线工作</li>
                            <li><strong>数据同步：</strong> 在网络恢复后自动同步离线数据</li>
                            <li><strong>推送通知：</strong> 通过推送通知提醒用户重要事项</li>
                            <li><strong>扫码功能：</strong> 支持扫描NFC标签或二维码</li>
                            <li><strong>拍照功能：</strong> 支持拍照记录巡检情况</li>
                            <li><strong>定位功能：</strong> 支持GPS定位和室内定位</li>
                        </ul>
                        <p class="implementation-note">实现要点：移动端应考虑电量和流量消耗，优化应用性能</p>
                    </div>

                    <div class="description-section">
                        <h4>10. 权限控制</h4>
                        <p>权限控制确保不同角色的用户只能访问和操作其权限范围内的功能，包含以下方面：</p>
                        <ul>
                            <li><strong>角色定义：</strong> 定义不同的用户角色，如管理员、巡检主管、巡检人员等</li>
                            <li><strong>功能权限：</strong> 控制不同角色可以访问的功能模块</li>
                            <li><strong>数据权限：</strong> 控制不同角色可以查看和操作的数据范围</li>
                            <li><strong>操作权限：</strong> 控制不同角色可以执行的具体操作</li>
                            <li><strong>审批流程：</strong> 对重要操作设置审批流程，如路线变更、异常处理等</li>
                            <li><strong>操作日志：</strong> 记录用户的操作日志，便于追溯和审计</li>
                            <li><strong>权限继承：</strong> 支持权限的继承和委托，便于临时授权</li>
                            <li><strong>权限管理：</strong> 提供权限管理界面，便于管理员配置权限</li>
                        </ul>
                        <p class="implementation-note">实现要点：权限控制应细粒度到具体操作，并支持动态调整</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 监控视频管理 -->
        <div>
            <h2 class="screen-title">监控视频管理</h2>
            <div class="phone-frame">
                <iframe src="video.html"></iframe>
            </div>
        </div>

        <!-- 监控视频管理页面说明 -->
        <div>
            <h2 class="screen-title">监控视频管理页面说明</h2>
            <div class="login-description">
                <div class="description-header">
                    <h3>监控视频管理页面功能说明</h3>
                    <p class="description-subtitle">针对开发人员的实现指南</p>
                </div>

                <div class="description-content">
                    <div class="description-section">
                        <h4>1. 页面结构</h4>
                        <p>监控视频管理页面用于实时查看、回放和管理监控摄像头，包含以下主要部分：</p>
                        <ul>
                            <li><strong>顶部标题栏：</strong> 显示"监控视频管理"标题和更多操作按钮</li>
                            <li><strong>搜索栏：</strong> 用于搜索摄像头或录像</li>
                            <li><strong>分段控制器：</strong> 用于切换实时监控、录像回放和设备管理三个标签</li>
                            <li><strong>筛选标签：</strong> 用于按状态或位置筛选摄像头</li>
                            <li><strong>视频播放器：</strong> 用于播放选中的视频流</li>
                            <li><strong>摄像头列表：</strong> 按区域分组显示摄像头</li>
                        </ul>
                        <p class="implementation-note">实现要点：页面应支持横屏模式，在横屏时优化视频播放器布局</p>
                    </div>

                    <div class="description-section">
                        <h4>2. 实时监控功能</h4>
                        <p>实时监控标签页的功能实现：</p>
                        <ul>
                            <li><strong>视频流播放：</strong> 支持RTSP、RTMP、HLS等主流视频流协议</li>
                            <li><strong>多清晰度切换：</strong> 支持高清、标清、流畅等多种清晰度</li>
                            <li><strong>PTZ控制：</strong> 对于云台摄像头，支持平移、倾斜、缩放控制</li>
                            <li><strong>截图功能：</strong> 支持对当前画面进行截图并保存</li>
                            <li><strong>录制功能：</strong> 支持手动开始/停止录制当前视频流</li>
                            <li><strong>全屏播放：</strong> 支持全屏模式播放视频</li>
                            <li><strong>多画面分割：</strong> 支持1、4、9、16等多画面分割模式</li>
                        </ul>
                        <p class="implementation-note">实现要点：视频流播放应优化带宽使用，根据网络状况自动调整清晰度</p>
                    </div>

                    <div class="description-section">
                        <h4>3. 录像回放功能</h4>
                        <p>录像回放标签页的功能实现：</p>
                        <ul>
                            <li><strong>日期选择：</strong> 支持选择特定日期的录像</li>
                            <li><strong>时间轴：</strong> 显示录像的时间分布，标记有录像的时间段</li>
                            <li><strong>录像列表：</strong> 显示所选日期的录像列表，包含缩略图、时间、时长等信息</li>
                            <li><strong>回放控制：</strong> 支持播放、暂停、快进、快退、跳转等操作</li>
                            <li><strong>录像下载：</strong> 支持下载录像到本地设备</li>
                            <li><strong>录像剪辑：</strong> 支持剪辑录像片段并保存</li>
                            <li><strong>录像恢复：</strong> 支持恢复已删除的录像（取决于存储策略）</li>
                        </ul>
                        <p class="implementation-note">实现要点：录像回放应支持缓存机制，提高回放流畅度</p>
                    </div>

                    <div class="description-section">
                        <h4>4. 设备管理功能</h4>
                        <p>设备管理标签页的功能实现：</p>
                        <ul>
                            <li><strong>设备列表：</strong> 显示所有摄像头设备，包含状态、型号、IP地址等信息</li>
                            <li><strong>设备分组：</strong> 支持按区域、类型等对设备进行分组管理</li>
                            <li><strong>设备状态：</strong> 显示设备的在线状态、录制状态、存储状态等</li>
                            <li><strong>设备配置：</strong> 支持配置设备参数，如分辨率、帧率、码率等</li>
                            <li><strong>存储管理：</strong> 配置录像的存储策略，如循环覆盖、保留天数等</li>
                            <li><strong>设备维护：</strong> 支持重启、升级、恢复出厂设置等维护操作</li>
                            <li><strong>权限管理：</strong> 配置不同用户对设备的访问权限</li>
                        </ul>
                        <p class="implementation-note">实现要点：设备管理操作应有权限控制，防止未授权操作</p>
                    </div>

                    <div class="description-section">
                        <h4>5. 摄像头卡片</h4>
                        <p>摄像头卡片的设计和功能：</p>
                        <ul>
                            <li><strong>缩略图：</strong> 显示摄像头的实时预览图或最后一帧图像</li>
                            <li><strong>播放按钮：</strong> 点击后在主播放器中播放该摄像头的视频流</li>
                            <li><strong>状态标签：</strong> 显示摄像头的状态（在线、离线、录制中）</li>
                            <li><strong>基本信息：</strong> 显示摄像头名称、位置等基本信息</li>
                            <li><strong>时间信息：</strong> 显示实时状态或最后在线时间</li>
                        </ul>
                        <p class="implementation-note">实现要点：缩略图应使用低分辨率图像或定期刷新的静态图像，减少带宽占用</p>
                    </div>

                    <div class="description-section">
                        <h4>6. 录像卡片</h4>
                        <p>录像回放标签页中的录像卡片设计和功能：</p>
                        <ul>
                            <li><strong>缩略图：</strong> 显示录像的关键帧或首帧</li>
                            <li><strong>录像信息：</strong> 显示录像的名称、摄像头、时间段等信息</li>
                            <li><strong>时长信息：</strong> 显示录像的总时长</li>
                            <li><strong>状态标签：</strong> 显示录像的状态（正常、已归档、已删除等）</li>
                            <li><strong>操作按钮：</strong> 提供播放、下载、删除等操作</li>
                        </ul>
                        <p class="implementation-note">实现要点：录像卡片应支持批量操作，如批量下载、批量删除等</p>
                    </div>

                    <div class="description-section">
                        <h4>7. 设备卡片</h4>
                        <p>设备管理标签页中的设备卡片设计和功能：</p>
                        <ul>
                            <li><strong>设备图标：</strong> 显示设备类型的图标</li>
                            <li><strong>设备信息：</strong> 显示设备名称、型号、ID等基本信息</li>
                            <li><strong>状态标签：</strong> 显示设备的状态（在线、离线、维护中）</li>
                            <li><strong>设备参数：</strong> 显示关键参数，如IP地址、分辨率等</li>
                            <li><strong>统计信息：</strong> 显示设备的使用统计，如运行时间、存储使用量等</li>
                            <li><strong>操作按钮：</strong> 提供配置、重启、升级等操作</li>
                        </ul>
                        <p class="implementation-note">实现要点：设备卡片应显示设备健康状态，如CPU使用率、内存使用率等</p>
                    </div>

                    <div class="description-section">
                        <h4>8. 视频播放器</h4>
                        <p>主视频播放器的设计和功能：</p>
                        <ul>
                            <li><strong>播放控制：</strong> 提供播放、暂停、停止等基本控制</li>
                            <li><strong>进度条：</strong> 在回放模式下显示播放进度，支持拖动跳转</li>
                            <li><strong>音量控制：</strong> 支持调节音量和静音</li>
                            <li><strong>清晰度切换：</strong> 支持切换不同的视频清晰度</li>
                            <li><strong>全屏按钮：</strong> 支持切换全屏模式</li>
                            <li><strong>PTZ控制：</strong> 对于支持的摄像头，显示PTZ控制面板</li>
                            <li><strong>工具按钮：</strong> 提供截图、录制、设置等工具按钮</li>
                        </ul>
                        <p class="implementation-note">实现要点：播放器应支持手势控制，如双指缩放、滑动调节进度等</p>
                    </div>

                    <div class="description-section">
                        <h4>9. 录像恢复功能</h4>
                        <p>录像恢复功能的设计和实现：</p>
                        <ul>
                            <li><strong>回收站：</strong> 显示已删除但可恢复的录像列表</li>
                            <li><strong>筛选功能：</strong> 支持按日期、摄像头等条件筛选已删除的录像</li>
                            <li><strong>预览功能：</strong> 支持预览已删除的录像内容</li>
                            <li><strong>恢复操作：</strong> 支持单个或批量恢复录像</li>
                            <li><strong>永久删除：</strong> 支持永久删除不需要的录像</li>
                            <li><strong>自动清理：</strong> 根据存储策略自动清理过期的已删除录像</li>
                        </ul>
                        <p class="implementation-note">实现要点：录像恢复功能应考虑存储空间管理，避免恢复操作导致存储空间不足</p>
                    </div>

                    <div class="description-section">
                        <h4>10. 权限控制</h4>
                        <p>监控视频管理的权限控制机制：</p>
                        <ul>
                            <li><strong>查看权限：</strong> 控制用户可以查看哪些摄像头的视频</li>
                            <li><strong>回放权限：</strong> 控制用户可以回放哪些摄像头的录像</li>
                            <li><strong>下载权限：</strong> 控制用户可以下载哪些录像</li>
                            <li><strong>PTZ控制权限：</strong> 控制用户可以操作哪些摄像头的PTZ功能</li>
                            <li><strong>配置权限：</strong> 控制用户可以配置哪些设备的参数</li>
                            <li><strong>管理权限：</strong> 控制用户可以执行哪些管理操作，如添加、删除设备等</li>
                            <li><strong>优先级控制：</strong> 在多用户同时操作时，根据用户优先级决定控制权</li>
                        </ul>
                        <p class="implementation-note">实现要点：权限控制应细粒度到单个摄像头和具体操作，并支持基于角色的权限分配</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 系统配置 -->
        <div>
            <h2 class="screen-title">系统配置</h2>
            <div class="phone-frame">
                <iframe src="users.html"></iframe>
            </div>
        </div>

        <!-- 系统配置页面说明 -->
        <div>
            <h2 class="screen-title">系统配置页面说明</h2>
            <div class="login-description">
                <div class="description-header">
                    <h3>系统配置页面功能说明</h3>
                    <p class="description-subtitle">针对开发人员的实现指南</p>
                </div>

                <div class="description-content">
                    <div class="description-section">
                        <h4>1. 页面结构</h4>
                        <p>系统配置页面用于管理系统的用户、部门、角色和权限等配置，包含以下主要部分：</p>
                        <ul>
                            <li><strong>顶部标题栏：</strong> 显示"系统配置"标题和添加按钮</li>
                            <li><strong>搜索栏：</strong> 用于搜索用户、部门或角色</li>
                            <li><strong>分段控制器：</strong> 用于切换用户管理、部门管理和角色管理三个标签</li>
                            <li><strong>筛选标签：</strong> 用于按类型或状态筛选列表内容</li>
                            <li><strong>列表内容：</strong> 显示用户、部门或角色的列表</li>
                        </ul>
                        <p class="implementation-note">实现要点：页面内容应根据用户权限动态调整，只显示用户有权限访问的功能</p>
                    </div>

                    <div class="description-section">
                        <h4>2. 用户管理功能</h4>
                        <p>用户管理标签页的功能实现：</p>
                        <ul>
                            <li><strong>用户列表：</strong> 显示系统中的所有用户，包含头像、姓名、角色和状态等信息</li>
                            <li><strong>用户筛选：</strong> 支持按角色（管理员、运维人员、普通用户等）和状态（在职、离职）筛选用户</li>
                            <li><strong>用户搜索：</strong> 支持按姓名、工号、部门等关键词搜索用户</li>
                            <li><strong>用户详情：</strong> 点击用户项可查看用户详细信息</li>
                            <li><strong>用户创建：</strong> 支持创建新用户，设置基本信息、角色和权限</li>
                            <li><strong>用户编辑：</strong> 支持编辑现有用户的信息、角色和权限</li>
                            <li><strong>用户停用/启用：</strong> 支持停用或启用用户账号</li>
                            <li><strong>密码重置：</strong> 支持重置用户密码</li>
                        </ul>
                        <p class="implementation-note">实现要点：用户管理操作应记录操作日志，便于审计和追溯</p>
                    </div>

                    <div class="description-section">
                        <h4>3. 部门管理功能</h4>
                        <p>部门管理标签页的功能实现：</p>
                        <ul>
                            <li><strong>部门列表：</strong> 显示系统中的所有部门，包含部门名称、负责人、成员数量等信息</li>
                            <li><strong>部门筛选：</strong> 支持按部门类型、状态等条件筛选部门</li>
                            <li><strong>部门搜索：</strong> 支持按部门名称、负责人等关键词搜索部门</li>
                            <li><strong>部门详情：</strong> 点击部门项可查看部门详细信息，包括成员列表</li>
                            <li><strong>部门创建：</strong> 支持创建新部门，设置部门名称、负责人、上级部门等</li>
                            <li><strong>部门编辑：</strong> 支持编辑现有部门的信息</li>
                            <li><strong>部门合并：</strong> 支持将两个或多个部门合并</li>
                            <li><strong>部门解散：</strong> 支持解散部门，并处理部门成员的归属</li>
                            <li><strong>组织架构图：</strong> 支持以树形图方式查看组织架构</li>
                        </ul>
                        <p class="implementation-note">实现要点：部门管理应支持多级部门结构，并处理部门变更时的数据一致性</p>
                    </div>

                    <div class="description-section">
                        <h4>4. 角色管理功能</h4>
                        <p>角色管理标签页的功能实现：</p>
                        <ul>
                            <li><strong>角色列表：</strong> 显示系统中的所有角色，包含角色名称、描述、用户数量等信息</li>
                            <li><strong>角色筛选：</strong> 支持按角色类型、状态等条件筛选角色</li>
                            <li><strong>角色搜索：</strong> 支持按角色名称、描述等关键词搜索角色</li>
                            <li><strong>角色详情：</strong> 点击角色项可查看角色详细信息，包括权限列表和用户列表</li>
                            <li><strong>角色创建：</strong> 支持创建新角色，设置角色名称、描述和权限</li>
                            <li><strong>角色编辑：</strong> 支持编辑现有角色的信息和权限</li>
                            <li><strong>角色复制：</strong> 支持基于现有角色创建新角色</li>
                            <li><strong>角色删除：</strong> 支持删除不再使用的角色</li>
                            <li><strong>权限配置：</strong> 支持为角色配置详细的功能权限和数据权限</li>
                        </ul>
                        <p class="implementation-note">实现要点：角色管理应支持权限的细粒度控制，包括功能权限和数据权限</p>
                    </div>

                    <div class="description-section">
                        <h4>5. 权限管理功能</h4>
                        <p>权限管理的功能实现（通常集成在角色管理中）：</p>
                        <ul>
                            <li><strong>权限列表：</strong> 显示系统中的所有权限，按模块和功能分组</li>
                            <li><strong>权限分类：</strong> 将权限分为功能权限和数据权限两大类</li>
                            <li><strong>功能权限：</strong> 控制用户可以访问哪些功能模块和执行哪些操作</li>
                            <li><strong>数据权限：</strong> 控制用户可以访问哪些数据范围，如本人数据、部门数据、全部数据等</li>
                            <li><strong>权限继承：</strong> 支持权限的继承关系，简化权限配置</li>
                            <li><strong>权限冲突解决：</strong> 处理用户拥有多个角色时的权限冲突</li>
                            <li><strong>权限预览：</strong> 支持预览特定用户或角色的实际权限</li>
                            <li><strong>权限变更记录：</strong> 记录权限变更历史，便于审计</li>
                        </ul>
                        <p class="implementation-note">实现要点：权限控制应在前端和后端都实现，确保安全性</p>
                    </div>

                    <div class="description-section">
                        <h4>6. 用户卡片</h4>
                        <p>用户管理标签页中的用户卡片设计和功能：</p>
                        <ul>
                            <li><strong>用户头像：</strong> 显示用户的头像或默认图标</li>
                            <li><strong>用户姓名：</strong> 显示用户的姓名</li>
                            <li><strong>用户角色：</strong> 显示用户的主要角色</li>
                            <li><strong>用户状态：</strong> 显示用户的状态（在职、离职）</li>
                            <li><strong>点击行为：</strong> 点击卡片跳转到用户详情页面</li>
                            <li><strong>长按行为：</strong> 长按卡片显示快捷操作菜单</li>
                            <li><strong>滑动行为：</strong> 支持左右滑动显示快捷操作按钮</li>
                        </ul>
                        <p class="implementation-note">实现要点：用户卡片应支持批量选择，便于批量操作</p>
                    </div>

                    <div class="description-section">
                        <h4>7. 部门卡片</h4>
                        <p>部门管理标签页中的部门卡片设计和功能：</p>
                        <ul>
                            <li><strong>部门图标：</strong> 显示部门的图标</li>
                            <li><strong>部门名称：</strong> 显示部门的名称</li>
                            <li><strong>部门信息：</strong> 显示部门的关键信息，如负责人、成员数量</li>
                            <li><strong>层级指示：</strong> 显示部门在组织架构中的层级</li>
                            <li><strong>展开/折叠：</strong> 支持展开或折叠子部门</li>
                            <li><strong>点击行为：</strong> 点击卡片跳转到部门详情页面</li>
                            <li><strong>操作按钮：</strong> 提供编辑、删除等操作按钮</li>
                        </ul>
                        <p class="implementation-note">实现要点：部门卡片应支持拖拽排序，调整部门顺序或层级关系</p>
                    </div>

                    <div class="description-section">
                        <h4>8. 角色卡片</h4>
                        <p>角色管理标签页中的角色卡片设计和功能：</p>
                        <ul>
                            <li><strong>角色图标：</strong> 显示角色的图标</li>
                            <li><strong>角色名称：</strong> 显示角色的名称</li>
                            <li><strong>角色描述：</strong> 显示角色的简短描述</li>
                            <li><strong>用户数量：</strong> 显示拥有该角色的用户数量</li>
                            <li><strong>点击行为：</strong> 点击卡片跳转到角色详情页面</li>
                            <li><strong>操作按钮：</strong> 提供编辑、复制、删除等操作按钮</li>
                        </ul>
                        <p class="implementation-note">实现要点：角色卡片应显示角色的权限概要，便于快速了解角色权限</p>
                    </div>

                    <div class="description-section">
                        <h4>9. 系统参数配置</h4>
                        <p>系统参数配置的功能实现（可能作为单独的标签页或子页面）：</p>
                        <ul>
                            <li><strong>基本参数：</strong> 配置系统的基本参数，如系统名称、Logo、主题色等</li>
                            <li><strong>安全参数：</strong> 配置系统的安全参数，如密码策略、登录策略、会话超时等</li>
                            <li><strong>通知参数：</strong> 配置系统的通知参数，如邮件服务器、短信服务等</li>
                            <li><strong>集成参数：</strong> 配置与第三方系统的集成参数，如单点登录、API密钥等</li>
                            <li><strong>日志参数：</strong> 配置系统的日志参数，如日志级别、保留期限等</li>
                            <li><strong>备份参数：</strong> 配置系统的备份参数，如备份周期、备份位置等</li>
                            <li><strong>参数导入导出：</strong> 支持参数配置的导入和导出</li>
                            <li><strong>参数历史：</strong> 记录参数变更历史，支持回滚</li>
                        </ul>
                        <p class="implementation-note">实现要点：系统参数应分环境存储，支持开发、测试、生产等不同环境的配置</p>
                    </div>

                    <div class="description-section">
                        <h4>10. 操作日志与审计</h4>
                        <p>操作日志与审计功能的实现（可能作为单独的标签页或子页面）：</p>
                        <ul>
                            <li><strong>操作日志列表：</strong> 显示系统中的所有操作日志，包含操作人、操作时间、操作类型、操作内容等信息</li>
                            <li><strong>日志筛选：</strong> 支持按操作人、操作类型、时间范围等条件筛选日志</li>
                            <li><strong>日志搜索：</strong> 支持按关键词搜索日志内容</li>
                            <li><strong>日志详情：</strong> 点击日志项可查看详细信息，包括操作前后的数据对比</li>
                            <li><strong>日志导出：</strong> 支持将日志导出为Excel或PDF格式</li>
                            <li><strong>日志归档：</strong> 支持将历史日志归档，减轻系统负担</li>
                            <li><strong>审计报告：</strong> 支持生成审计报告，用于合规检查</li>
                            <li><strong>异常监控：</strong> 监控异常操作，及时发现安全风险</li>
                        </ul>
                        <p class="implementation-note">实现要点：操作日志应记录详细的操作内容，包括操作前后的数据变化</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 我的 -->
        <div>
            <h2 class="screen-title">我的</h2>
            <div class="phone-frame">
                <iframe src="profile.html"></iframe>
            </div>
        </div>

        <!-- 我的页面说明 -->
        <div>
            <h2 class="screen-title">我的页面说明</h2>
            <div class="login-description">
                <div class="description-header">
                    <h3>我的页面功能说明</h3>
                    <p class="description-subtitle">针对开发人员的实现指南</p>
                </div>

                <div class="description-content">
                    <div class="description-section">
                        <h4>1. 页面结构</h4>
                        <p>"我的"页面是用户个人中心，提供个人信息管理和系统功能入口，包含以下主要部分：</p>
                        <ul>
                            <li><strong>顶部标题栏：</strong> 显示"我的"标题和设置按钮</li>
                            <li><strong>个人资料卡片：</strong> 显示用户头像、姓名、角色和所属公司</li>
                            <li><strong>功能菜单区：</strong> 分为账户管理、我的项目、支持与帮助三个部分</li>
                            <li><strong>版本信息：</strong> 显示应用的版本号和构建信息</li>
                            <li><strong>底部导航栏：</strong> 提供主要功能区域的快速访问</li>
                        </ul>
                        <p class="implementation-note">实现要点：页面内容应根据用户角色动态调整，只显示用户有权限访问的功能</p>
                    </div>

                    <div class="description-section">
                        <h4>2. 个人资料卡片</h4>
                        <p>个人资料卡片的设计和功能：</p>
                        <ul>
                            <li><strong>用户头像：</strong> 显示用户的头像，支持点击查看大图或更换头像</li>
                            <li><strong>用户姓名：</strong> 显示用户的真实姓名</li>
                            <li><strong>用户角色：</strong> 显示用户的主要角色，如系统管理员、运维人员等</li>
                            <li><strong>所属公司：</strong> 显示用户所属的公司或组织</li>
                            <li><strong>点击行为：</strong> 点击卡片可跳转到个人信息详情页面</li>
                        </ul>
                        <p class="implementation-note">实现要点：头像应支持缓存和懒加载，提高页面加载速度</p>
                    </div>

                    <div class="description-section">
                        <h4>3. 账户管理功能</h4>
                        <p>账户管理菜单区的功能实现：</p>
                        <ul>
                            <li><strong>个人信息：</strong> 跳转到个人信息页面，支持查看和编辑个人基本信息</li>
                            <li><strong>修改密码：</strong> 跳转到密码修改页面，支持修改登录密码</li>
                            <li><strong>消息通知：</strong> 跳转到通知设置页面，支持配置接收哪些类型的通知</li>
                        </ul>
                        <p class="implementation-note">实现要点：个人信息修改应有数据验证，确保数据的正确性和安全性</p>
                    </div>

                    <div class="description-section">
                        <h4>4. 个人信息功能</h4>
                        <p>个人信息页面的功能实现：</p>
                        <ul>
                            <li><strong>基本信息：</strong> 包括姓名、性别、生日、手机号、邮箱等基本信息</li>
                            <li><strong>头像管理：</strong> 支持上传、裁剪和设置个人头像</li>
                            <li><strong>联系方式：</strong> 管理联系电话、邮箱、即时通讯账号等联系方式</li>
                            <li><strong>个人简介：</strong> 编辑个人简介或自我介绍</li>
                            <li><strong>职业信息：</strong> 包括职位、部门、工号等职业相关信息</li>
                            <li><strong>技能标签：</strong> 添加和管理个人技能标签</li>
                        </ul>
                        <p class="implementation-note">实现要点：个人信息应支持部分字段的隐私设置，控制信息的可见范围</p>
                    </div>

                    <div class="description-section">
                        <h4>5. 密码修改功能</h4>
                        <p>密码修改页面的功能实现：</p>
                        <ul>
                            <li><strong>当前密码验证：</strong> 要求输入当前密码进行身份验证</li>
                            <li><strong>新密码设置：</strong> 设置新的登录密码</li>
                            <li><strong>密码确认：</strong> 再次输入新密码进行确认</li>
                            <li><strong>密码强度检测：</strong> 实时检测并显示密码强度</li>
                            <li><strong>密码规则提示：</strong> 显示密码设置规则，如最小长度、必须包含的字符类型等</li>
                            <li><strong>验证码验证：</strong> 可选功能，通过短信或邮箱验证码进行二次验证</li>
                        </ul>
                        <p class="implementation-note">实现要点：密码修改成功后应自动更新会话状态，无需用户重新登录</p>
                    </div>

                    <div class="description-section">
                        <h4>6. 消息通知功能</h4>
                        <p>消息通知页面的功能实现：</p>
                        <ul>
                            <li><strong>通知列表：</strong> 显示所有接收到的通知消息</li>
                            <li><strong>通知分类：</strong> 按类型分类显示通知，如系统通知、项目通知、任务通知等</li>
                            <li><strong>通知设置：</strong> 配置接收哪些类型的通知</li>
                            <li><strong>通知方式：</strong> 设置通知的接收方式，如应用内、短信、邮件等</li>
                            <li><strong>免打扰时段：</strong> 设置不接收通知的时间段</li>
                            <li><strong>通知管理：</strong> 支持标记已读、删除、全部已读等操作</li>
                        </ul>
                        <p class="implementation-note">实现要点：通知系统应支持实时推送，可考虑使用WebSocket或推送服务</p>
                    </div>

                    <div class="description-section">
                        <h4>7. 我的项目功能</h4>
                        <p>我的项目菜单区的功能实现：</p>
                        <ul>
                            <li><strong>项目列表：</strong> 跳转到项目列表页面，显示用户参与的所有项目</li>
                            <li><strong>数据统计：</strong> 跳转到数据统计页面，显示用户的工作数据统计</li>
                            <li><strong>操作记录：</strong> 跳转到操作记录页面，显示用户的操作历史</li>
                        </ul>
                        <p class="implementation-note">实现要点：项目列表应支持多种视图模式，如列表视图、卡片视图等</p>
                    </div>

                    <div class="description-section">
                        <h4>8. 项目列表功能</h4>
                        <p>项目列表页面的功能实现：</p>
                        <ul>
                            <li><strong>项目分类：</strong> 按状态分类显示项目，如进行中、已完成、已归档等</li>
                            <li><strong>项目搜索：</strong> 支持按项目名称、编号等关键词搜索项目</li>
                            <li><strong>项目筛选：</strong> 支持按时间、类型、角色等条件筛选项目</li>
                            <li><strong>项目排序：</strong> 支持按创建时间、更新时间、项目名称等排序</li>
                            <li><strong>项目卡片：</strong> 显示项目的基本信息，如名称、进度、截止日期等</li>
                            <li><strong>快速操作：</strong> 提供查看详情、进入项目等快速操作</li>
                        </ul>
                        <p class="implementation-note">实现要点：项目列表应支持分页加载或无限滚动，优化大量数据的加载性能</p>
                    </div>

                    <div class="description-section">
                        <h4>9. 支持与帮助功能</h4>
                        <p>支持与帮助菜单区的功能实现：</p>
                        <ul>
                            <li><strong>帮助中心：</strong> 跳转到帮助中心页面，提供使用指南和常见问题解答</li>
                            <li><strong>联系客服：</strong> 提供多种联系客服的方式，如在线聊天、电话、邮件等</li>
                            <li><strong>退出登录：</strong> 退出当前账号的登录状态，返回登录页面</li>
                        </ul>
                        <p class="implementation-note">实现要点：退出登录应清除所有本地存储的敏感数据，确保安全性</p>
                    </div>

                    <div class="description-section">
                        <h4>10. 设置功能</h4>
                        <p>通过顶部标题栏的设置按钮访问的设置功能：</p>
                        <ul>
                            <li><strong>界面设置：</strong> 配置应用的界面相关设置，如主题色、字体大小等</li>
                            <li><strong>语言设置：</strong> 选择应用的显示语言</li>
                            <li><strong>隐私设置：</strong> 配置个人信息的隐私级别</li>
                            <li><strong>安全设置：</strong> 配置安全相关选项，如生物识别登录、二次验证等</li>
                            <li><strong>存储管理：</strong> 查看和清理应用缓存和存储</li>
                            <li><strong>关于应用：</strong> 显示应用的详细信息，如版本号、开发者信息等</li>
                            <li><strong>检查更新：</strong> 检查应用是否有新版本可用</li>
                        </ul>
                        <p class="implementation-note">实现要点：设置应实时保存并立即生效，提供良好的用户体验</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
