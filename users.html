<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理 - 集成商登陆平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        .search-bar {
            display: flex;
            align-items: center;
            background-color: var(--light-gray);
            border-radius: 10px;
            padding: 8px 12px;
            margin-bottom: 16px;
        }

        .search-bar input {
            flex: 1;
            border: none;
            background: transparent;
            margin-left: 8px;
            font-size: 15px;
        }

        .search-bar input:focus {
            outline: none;
        }

        .segment-control {
            display: flex;
            background-color: var(--light-gray);
            border-radius: 10px;
            padding: 4px;
            margin-bottom: 20px;
        }

        .segment-item {
            flex: 1;
            text-align: center;
            padding: 8px 0;
            font-size: 14px;
            border-radius: 8px;
        }

        .segment-item.active {
            background-color: white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            font-weight: 600;
        }

        .user-item {
            display: flex;
            align-items: center;
            padding: 16px;
            border-bottom: 1px solid var(--light-gray);
        }

        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 25px;
            background-color: var(--light-gray);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            overflow: hidden;
        }

        .user-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .user-avatar-placeholder {
            color: var(--dark-gray);
            font-size: 20px;
        }

        .user-info {
            flex: 1;
        }

        .user-name {
            font-size: 17px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .user-role {
            font-size: 14px;
            color: var(--dark-gray);
        }

        .user-status {
            font-size: 14px;
            padding: 4px 10px;
            border-radius: 100px;
        }

        .user-status.active {
            background-color: rgba(52, 199, 89, 0.1);
            color: var(--success-color);
        }

        .user-status.inactive {
            background-color: rgba(142, 142, 147, 0.1);
            color: var(--dark-gray);
        }

        .filter-tabs {
            display: flex;
            overflow-x: auto;
            margin-bottom: 16px;
            -webkit-overflow-scrolling: touch;
        }

        .filter-tab {
            padding: 8px 16px;
            border-radius: 100px;
            font-size: 14px;
            white-space: nowrap;
            margin-right: 8px;
        }

        .filter-tab.active {
            background-color: var(--primary-color);
            color: white;
        }

        .filter-tab:not(.active) {
            background-color: var(--light-gray);
            color: var(--dark-gray);
        }

        /* 标签内容区域 */
        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* 部门管理样式 */
        .department-item {
            display: flex;
            align-items: center;
            padding: 16px;
            border-bottom: 1px solid var(--light-gray);
            transition: all 0.2s ease;
            position: relative;
        }

        .department-item:hover {
            background-color: rgba(0, 122, 255, 0.03);
        }

        .department-item:active {
            background-color: rgba(0, 122, 255, 0.05);
        }

        .department-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            font-size: 20px;
            color: white;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 1;
            transition: all 0.2s ease;
        }

        .department-icon::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 12px;
            background: linear-gradient(135deg, var(--primary-color), #5AC8FA);
            z-index: -1;
        }

        .department-icon.hq::before {
            background: linear-gradient(135deg, #FF9500, #FF2D55);
        }

        .department-icon.branch::before {
            background: linear-gradient(135deg, #5856D6, #AF52DE);
        }

        .department-icon.tech::before {
            background: linear-gradient(135deg, #007AFF, #5AC8FA);
        }

        .department-icon.market::before {
            background: linear-gradient(135deg, #34C759, #32D74B);
        }

        .department-info {
            flex: 1;
            padding-right: 12px;
        }

        .department-name {
            font-size: 17px;
            font-weight: 600;
            margin-bottom: 6px;
            display: flex;
            align-items: center;
        }

        .department-type {
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 100px;
            background-color: rgba(0, 122, 255, 0.1);
            color: var(--primary-color);
            margin-left: 8px;
            font-weight: normal;
        }

        .department-details {
            font-size: 14px;
            color: var(--dark-gray);
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            gap: 12px;
        }

        .department-detail-item {
            display: flex;
            align-items: center;
        }

        .department-detail-item i {
            margin-right: 6px;
            font-size: 14px;
            color: var(--primary-color);
            opacity: 0.8;
        }

        .department-actions {
            display: flex;
            margin-top: 8px;
            gap: 8px;
        }

        .department-action-btn {
            font-size: 12px;
            color: var(--primary-color);
            background-color: rgba(0, 122, 255, 0.08);
            padding: 4px 8px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            transition: all 0.2s ease;
        }

        .department-action-btn:hover {
            background-color: rgba(0, 122, 255, 0.12);
        }

        .department-action-btn i {
            margin-right: 4px;
            font-size: 10px;
        }

        .department-status {
            font-size: 13px;
            padding: 4px 10px;
            border-radius: 100px;
            font-weight: 500;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .department-status.active {
            background-color: rgba(52, 199, 89, 0.15);
            color: var(--success-color);
        }

        .department-status.inactive {
            background-color: rgba(142, 142, 147, 0.15);
            color: var(--dark-gray);
        }

        .department-children {
            margin-left: 25px;
            padding-left: 41px;
            position: relative;
        }

        .department-children::before {
            content: '';
            position: absolute;
            top: 0;
            left: 25px;
            bottom: 0;
            width: 2px;
            background: linear-gradient(to bottom,
                rgba(0, 122, 255, 0.3) 0%,
                rgba(0, 122, 255, 0.1) 100%);
            border-radius: 1px;
        }

        .department-children .department-item::before {
            content: '';
            position: absolute;
            top: 50%;
            left: -16px;
            width: 16px;
            height: 2px;
            background: linear-gradient(to right,
                rgba(0, 122, 255, 0.1) 0%,
                rgba(0, 122, 255, 0.3) 100%);
            border-radius: 1px;
        }

        .department-toggle {
            position: absolute;
            right: 16px;
            top: 16px;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background-color: var(--light-gray);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--dark-gray);
            cursor: pointer;
            transition: all 0.2s ease;
            z-index: 2;
        }

        .department-toggle:hover {
            background-color: var(--medium-gray);
            color: var(--text-color);
        }

        .department-count {
            position: absolute;
            top: 16px;
            right: 50px;
            font-size: 13px;
            color: var(--dark-gray);
            background-color: var(--light-gray);
            padding: 2px 8px;
            border-radius: 100px;
        }

        /* 角色管理样式 */
        .role-item {
            display: flex;
            align-items: center;
            padding: 16px;
            border-bottom: 1px solid var(--light-gray);
        }

        .role-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            font-size: 20px;
            color: white;
        }

        .role-icon.admin {
            background: linear-gradient(135deg, #FF2D55, #FF9500);
        }

        .role-icon.manager {
            background: linear-gradient(135deg, #5856D6, #AF52DE);
        }

        .role-icon.operator {
            background: linear-gradient(135deg, #007AFF, #5AC8FA);
        }

        .role-icon.user {
            background: linear-gradient(135deg, #34C759, #32D74B);
        }

        .role-info {
            flex: 1;
        }

        .role-name {
            font-size: 17px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .role-description {
            font-size: 14px;
            color: var(--dark-gray);
        }

        .role-count {
            font-size: 14px;
            padding: 4px 10px;
            border-radius: 100px;
            background-color: var(--light-gray);
            color: var(--dark-gray);
        }

        .role-permissions {
            margin-top: 8px;
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
        }

        .permission-badge {
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 100px;
            background-color: var(--light-gray);
            color: var(--dark-gray);
        }

        /* 弹窗样式 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            overflow-y: auto;
            animation: fadeIn 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .modal-content {
            position: relative;
            background-color: white;
            margin: 10% auto;
            width: 90%;
            max-width: 500px;
            border-radius: 16px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            animation: slideIn 0.3s ease;
        }

        @keyframes slideIn {
            from { transform: translateY(-50px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        .modal-header {
            padding: 16px 20px;
            border-bottom: 1px solid var(--light-gray);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
        }

        .close {
            font-size: 24px;
            font-weight: 600;
            color: var(--dark-gray);
            cursor: pointer;
        }

        .close:hover {
            color: var(--text-color);
        }

        .modal-body {
            padding: 20px;
            max-height: 60vh;
            overflow-y: auto;
        }

        .modal-footer {
            padding: 16px 20px;
            border-top: 1px solid var(--light-gray);
            display: flex;
            justify-content: flex-end;
            gap: 12px;
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: 16px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            font-size: 15px;
        }

        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid var(--light-gray);
            border-radius: 8px;
            font-size: 15px;
            transition: border-color 0.2s ease;
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            border-color: var(--primary-color);
            outline: none;
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .btn {
            padding: 10px 16px;
            border-radius: 8px;
            font-size: 15px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            border: none;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background-color: #0062cc;
        }

        .btn-cancel {
            background-color: var(--light-gray);
            color: var(--text-color);
        }

        .btn-cancel:hover {
            background-color: var(--medium-gray);
        }

        /* 权限选择样式 */
        .permission-groups {
            border: 1px solid var(--light-gray);
            border-radius: 8px;
            max-height: 300px;
            overflow-y: auto;
        }

        .permission-group {
            border-bottom: 1px solid var(--light-gray);
        }

        .permission-group:last-child {
            border-bottom: none;
        }

        .permission-group-header {
            padding: 12px 16px;
            background-color: rgba(0, 122, 255, 0.05);
            display: flex;
            align-items: center;
            font-weight: 500;
        }

        .permission-group-header label {
            margin-bottom: 0;
            margin-left: 8px;
        }

        .permission-group-items {
            padding: 8px 16px 8px 36px;
        }

        .permission-item {
            padding: 8px 0;
            display: flex;
            align-items: center;
        }

        .permission-item label {
            margin-bottom: 0;
            margin-left: 8px;
            font-weight: normal;
        }

        .permission-group-checkbox, .permission-item-checkbox {
            width: 18px;
            height: 18px;
            cursor: pointer;
        }

        /* 浮动新增按钮样式 */
        .add-button {
            position: fixed;
            bottom: 100px;
            right: 20px;
            width: 56px;
            height: 56px;
            border-radius: 50%;
            background-color: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
            z-index: 50;
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .add-button:active {
            transform: scale(0.95);
            box-shadow: 0 2px 8px rgba(0, 122, 255, 0.2);
        }

        .add-button.animate-pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(0, 122, 255, 0.5);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(0, 122, 255, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(0, 122, 255, 0);
            }
        }
    </style>
</head>
<body>
    <!-- Status Bar -->
    <div class="status-bar">
        <div>9:41</div>
        <div class="status-bar-icons">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>

    <!-- Header -->
    <div class="header">
        <div class="header-title">系统配置</div>
        <div class="header-right">
            <i class="fas fa-search text-gray-600"></i>
        </div>
    </div>

    <!-- 新增角色弹窗 -->
    <div id="add-role-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>新增角色</h3>
                <span class="close" onclick="closeAddModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="role-name">角色名称</label>
                    <input type="text" id="role-name" placeholder="请输入角色名称" class="form-input">
                </div>
                <div class="form-group">
                    <label for="data-permission">数据权限</label>
                    <select id="data-permission" class="form-select">
                        <option value="all">全部数据</option>
                        <option value="department-and-children">部门及子部门数据</option>
                        <option value="department">本部门数据</option>
                        <option value="personal">本人数据</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="role-description">角色描述</label>
                    <textarea id="role-description" placeholder="请输入角色描述" class="form-textarea"></textarea>
                </div>
                <div class="form-group">
                    <label>功能权限</label>
                    <div class="permission-groups">
                        <div class="permission-group">
                            <div class="permission-group-header">
                                <input type="checkbox" id="system-management" class="permission-group-checkbox">
                                <label for="system-management">系统管理</label>
                            </div>
                            <div class="permission-group-items">
                                <div class="permission-item">
                                    <input type="checkbox" id="user-management" class="permission-item-checkbox">
                                    <label for="user-management">用户管理</label>
                                </div>
                                <div class="permission-item">
                                    <input type="checkbox" id="role-management" class="permission-item-checkbox">
                                    <label for="role-management">角色管理</label>
                                </div>
                                <div class="permission-item">
                                    <input type="checkbox" id="department-management" class="permission-item-checkbox">
                                    <label for="department-management">部门管理</label>
                                </div>
                            </div>
                        </div>
                        <div class="permission-group">
                            <div class="permission-group-header">
                                <input type="checkbox" id="device-management" class="permission-group-checkbox">
                                <label for="device-management">设备管理</label>
                            </div>
                            <div class="permission-group-items">
                                <div class="permission-item">
                                    <input type="checkbox" id="device-config" class="permission-item-checkbox">
                                    <label for="device-config">设备配置</label>
                                </div>
                                <div class="permission-item">
                                    <input type="checkbox" id="device-monitoring" class="permission-item-checkbox">
                                    <label for="device-monitoring">设备监控</label>
                                </div>
                            </div>
                        </div>
                        <div class="permission-group">
                            <div class="permission-group-header">
                                <input type="checkbox" id="data-management" class="permission-group-checkbox">
                                <label for="data-management">数据管理</label>
                            </div>
                            <div class="permission-group-items">
                                <div class="permission-item">
                                    <input type="checkbox" id="data-view" class="permission-item-checkbox">
                                    <label for="data-view">数据查看</label>
                                </div>
                                <div class="permission-item">
                                    <input type="checkbox" id="data-export" class="permission-item-checkbox">
                                    <label for="data-export">数据导出</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-cancel" onclick="closeAddModal()">取消</button>
                <button class="btn btn-primary" onclick="saveRole()">保存</button>
            </div>
        </div>
    </div>

    <!-- Content -->
    <div class="content">
        <!-- Search Bar -->
        <div class="search-bar">
            <i class="fas fa-search text-gray-400"></i>
            <input type="text" placeholder="搜索用户...">
        </div>

        <!-- Segment Control -->
        <div class="segment-control">
            <div class="segment-item active" onclick="switchTab('users')">用户管理</div>
            <div class="segment-item" onclick="switchTab('departments')">部门管理</div>
            <div class="segment-item" onclick="switchTab('roles')">角色管理</div>
        </div>

        <!-- 用户管理标签内容 -->
        <div id="users-tab" class="tab-content active">
            <!-- Filter Tabs -->
            <div class="filter-tabs">
                <div class="filter-tab active">全部</div>
                <div class="filter-tab">管理员</div>
                <div class="filter-tab">运维人员</div>
                <div class="filter-tab">普通用户</div>
            </div>

            <!-- User List -->
            <div class="card">
            <div class="user-item">
                <div class="user-avatar">
                    <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="User Avatar">
                </div>
                <div class="user-info">
                    <div class="user-name">张三</div>
                    <div class="user-role">系统管理员</div>
                </div>
                <div class="user-status active">在职</div>
            </div>

            <div class="user-item">
                <div class="user-avatar">
                    <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="User Avatar">
                </div>
                <div class="user-info">
                    <div class="user-name">李四</div>
                    <div class="user-role">运维主管</div>
                </div>
                <div class="user-status active">在职</div>
            </div>

            <div class="user-item">
                <div class="user-avatar">
                    <img src="https://randomuser.me/api/portraits/men/67.jpg" alt="User Avatar">
                </div>
                <div class="user-info">
                    <div class="user-name">王五</div>
                    <div class="user-role">设备管理员</div>
                </div>
                <div class="user-status active">在职</div>
            </div>

            <div class="user-item">
                <div class="user-avatar">
                    <img src="https://randomuser.me/api/portraits/women/33.jpg" alt="User Avatar">
                </div>
                <div class="user-info">
                    <div class="user-name">赵六</div>
                    <div class="user-role">运维工程师</div>
                </div>
                <div class="user-status active">在职</div>
            </div>

            <div class="user-item">
                <div class="user-avatar">
                    <div class="user-avatar-placeholder">
                        <i class="fas fa-user"></i>
                    </div>
                </div>
                <div class="user-info">
                    <div class="user-name">钱七</div>
                    <div class="user-role">普通用户</div>
                </div>
                <div class="user-status inactive">离职</div>
            </div>

            <div class="user-item">
                <div class="user-avatar">
                    <img src="https://randomuser.me/api/portraits/men/22.jpg" alt="User Avatar">
                </div>
                <div class="user-info">
                    <div class="user-name">孙八</div>
                    <div class="user-role">数据分析师</div>
                </div>
                <div class="user-status active">在职</div>
            </div>

            <div class="user-item">
                <div class="user-avatar">
                    <img src="https://randomuser.me/api/portraits/women/56.jpg" alt="User Avatar">
                </div>
                <div class="user-info">
                    <div class="user-name">周九</div>
                    <div class="user-role">项目经理</div>
                </div>
                <div class="user-status active">在职</div>
            </div>

            <div class="user-item">
                <div class="user-avatar">
                    <div class="user-avatar-placeholder">
                        <i class="fas fa-user"></i>
                    </div>
                </div>
                <div class="user-info">
                    <div class="user-name">吴十</div>
                    <div class="user-role">技术支持</div>
                </div>
                <div class="user-status inactive">离职</div>
            </div>
            </div>
        </div>

        <!-- 部门管理标签内容 -->
        <div id="departments-tab" class="tab-content">
            <!-- Filter Tabs -->
            <div class="filter-tabs">
                <div class="filter-tab active">全部</div>
                <div class="filter-tab">总部</div>
                <div class="filter-tab">分支机构</div>
                <div class="filter-tab">项目组</div>
            </div>

            <!-- Department List -->
            <div class="card">
                <!-- 总部 -->
                <div class="department-item">
                    <div class="department-icon hq">
                        <i class="fas fa-building"></i>
                    </div>
                    <div class="department-info">
                        <div class="department-name">
                            总部
                            <span class="department-type">总部机构</span>
                        </div>
                        <div class="department-details">
                            <div class="department-detail-item">
                                <i class="fas fa-user"></i>
                                <span>负责人: 张三</span>
                            </div>
                            <div class="department-detail-item">
                                <i class="fas fa-users"></i>
                                <span>成员: 25人</span>
                            </div>
                            <div class="department-detail-item">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>位置: 北京市朝阳区</span>
                            </div>
                        </div>
                        <div class="department-actions">
                            <div class="department-action-btn">
                                <i class="fas fa-edit"></i>
                                <span>编辑</span>
                            </div>
                            <div class="department-action-btn">
                                <i class="fas fa-user-plus"></i>
                                <span>添加成员</span>
                            </div>
                        </div>
                    </div>
                    <div class="department-count">3个子部门</div>
                    <div class="department-toggle">
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="department-status active">正常</div>
                </div>

                <!-- 技术部 -->
                <div class="department-children">
                    <div class="department-item">
                        <div class="department-icon tech">
                            <i class="fas fa-laptop-code"></i>
                        </div>
                        <div class="department-info">
                            <div class="department-name">
                                技术部
                                <span class="department-type">核心部门</span>
                            </div>
                            <div class="department-details">
                                <div class="department-detail-item">
                                    <i class="fas fa-user"></i>
                                    <span>负责人: 李四</span>
                                </div>
                                <div class="department-detail-item">
                                    <i class="fas fa-users"></i>
                                    <span>成员: 12人</span>
                                </div>
                                <div class="department-detail-item">
                                    <i class="fas fa-calendar-alt"></i>
                                    <span>创建时间: 2022-05-10</span>
                                </div>
                            </div>
                            <div class="department-actions">
                                <div class="department-action-btn">
                                    <i class="fas fa-edit"></i>
                                    <span>编辑</span>
                                </div>
                                <div class="department-action-btn">
                                    <i class="fas fa-user-plus"></i>
                                    <span>添加成员</span>
                                </div>
                            </div>
                        </div>
                        <div class="department-count">2个子部门</div>
                        <div class="department-toggle">
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="department-status active">正常</div>
                    </div>

                    <!-- 研发组 -->
                    <div class="department-children">
                        <div class="department-item">
                            <div class="department-icon tech">
                                <i class="fas fa-code"></i>
                            </div>
                            <div class="department-info">
                                <div class="department-name">
                                    研发组
                                    <span class="department-type">技术团队</span>
                                </div>
                                <div class="department-details">
                                    <div class="department-detail-item">
                                        <i class="fas fa-user"></i>
                                        <span>负责人: 王五</span>
                                    </div>
                                    <div class="department-detail-item">
                                        <i class="fas fa-users"></i>
                                        <span>成员: 8人</span>
                                    </div>
                                    <div class="department-detail-item">
                                        <i class="fas fa-project-diagram"></i>
                                        <span>项目: 5个</span>
                                    </div>
                                </div>
                                <div class="department-actions">
                                    <div class="department-action-btn">
                                        <i class="fas fa-edit"></i>
                                        <span>编辑</span>
                                    </div>
                                    <div class="department-action-btn">
                                        <i class="fas fa-user-plus"></i>
                                        <span>添加成员</span>
                                    </div>
                                </div>
                            </div>
                            <div class="department-status active">正常</div>
                        </div>

                        <!-- 测试组 -->
                        <div class="department-item">
                            <div class="department-icon tech">
                                <i class="fas fa-vial"></i>
                            </div>
                            <div class="department-info">
                                <div class="department-name">
                                    测试组
                                    <span class="department-type">技术团队</span>
                                </div>
                                <div class="department-details">
                                    <div class="department-detail-item">
                                        <i class="fas fa-user"></i>
                                        <span>负责人: 赵六</span>
                                    </div>
                                    <div class="department-detail-item">
                                        <i class="fas fa-users"></i>
                                        <span>成员: 4人</span>
                                    </div>
                                    <div class="department-detail-item">
                                        <i class="fas fa-bug"></i>
                                        <span>测试用例: 120个</span>
                                    </div>
                                </div>
                                <div class="department-actions">
                                    <div class="department-action-btn">
                                        <i class="fas fa-edit"></i>
                                        <span>编辑</span>
                                    </div>
                                    <div class="department-action-btn">
                                        <i class="fas fa-user-plus"></i>
                                        <span>添加成员</span>
                                    </div>
                                </div>
                            </div>
                            <div class="department-status active">正常</div>
                        </div>
                    </div>
                </div>

                <!-- 运维部 -->
                <div class="department-children">
                    <div class="department-item">
                        <div class="department-icon tech">
                            <i class="fas fa-server"></i>
                        </div>
                        <div class="department-info">
                            <div class="department-name">
                                运维部
                                <span class="department-type">核心部门</span>
                            </div>
                            <div class="department-details">
                                <div class="department-detail-item">
                                    <i class="fas fa-user"></i>
                                    <span>负责人: 钱七</span>
                                </div>
                                <div class="department-detail-item">
                                    <i class="fas fa-users"></i>
                                    <span>成员: 6人</span>
                                </div>
                                <div class="department-detail-item">
                                    <i class="fas fa-hdd"></i>
                                    <span>设备: 45台</span>
                                </div>
                            </div>
                            <div class="department-actions">
                                <div class="department-action-btn">
                                    <i class="fas fa-edit"></i>
                                    <span>编辑</span>
                                </div>
                                <div class="department-action-btn">
                                    <i class="fas fa-user-plus"></i>
                                    <span>添加成员</span>
                                </div>
                            </div>
                        </div>
                        <div class="department-status active">正常</div>
                    </div>
                </div>

                <!-- 市场部 -->
                <div class="department-children">
                    <div class="department-item">
                        <div class="department-icon market">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="department-info">
                            <div class="department-name">
                                市场部
                                <span class="department-type">核心部门</span>
                            </div>
                            <div class="department-details">
                                <div class="department-detail-item">
                                    <i class="fas fa-user"></i>
                                    <span>负责人: 孙八</span>
                                </div>
                                <div class="department-detail-item">
                                    <i class="fas fa-users"></i>
                                    <span>成员: 7人</span>
                                </div>
                                <div class="department-detail-item">
                                    <i class="fas fa-bullseye"></i>
                                    <span>营销活动: 3个</span>
                                </div>
                            </div>
                            <div class="department-actions">
                                <div class="department-action-btn">
                                    <i class="fas fa-edit"></i>
                                    <span>编辑</span>
                                </div>
                                <div class="department-action-btn">
                                    <i class="fas fa-user-plus"></i>
                                    <span>添加成员</span>
                                </div>
                            </div>
                        </div>
                        <div class="department-status active">正常</div>
                    </div>
                </div>

                <!-- 北京分公司 -->
                <div class="department-item">
                    <div class="department-icon branch">
                        <i class="fas fa-building"></i>
                    </div>
                    <div class="department-info">
                        <div class="department-name">
                            北京分公司
                            <span class="department-type">分支机构</span>
                        </div>
                        <div class="department-details">
                            <div class="department-detail-item">
                                <i class="fas fa-user"></i>
                                <span>负责人: 周九</span>
                            </div>
                            <div class="department-detail-item">
                                <i class="fas fa-users"></i>
                                <span>成员: 15人</span>
                            </div>
                            <div class="department-detail-item">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>位置: 北京市海淀区</span>
                            </div>
                        </div>
                        <div class="department-actions">
                            <div class="department-action-btn">
                                <i class="fas fa-edit"></i>
                                <span>编辑</span>
                            </div>
                            <div class="department-action-btn">
                                <i class="fas fa-user-plus"></i>
                                <span>添加成员</span>
                            </div>
                            <div class="department-action-btn">
                                <i class="fas fa-sitemap"></i>
                                <span>组织架构</span>
                            </div>
                        </div>
                    </div>
                    <div class="department-count">2个子部门</div>
                    <div class="department-toggle">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                    <div class="department-status active">正常</div>
                </div>

                <!-- 上海分公司 -->
                <div class="department-item">
                    <div class="department-icon branch">
                        <i class="fas fa-building"></i>
                    </div>
                    <div class="department-info">
                        <div class="department-name">
                            上海分公司
                            <span class="department-type">分支机构</span>
                        </div>
                        <div class="department-details">
                            <div class="department-detail-item">
                                <i class="fas fa-user"></i>
                                <span>负责人: 吴十</span>
                            </div>
                            <div class="department-detail-item">
                                <i class="fas fa-users"></i>
                                <span>成员: 12人</span>
                            </div>
                            <div class="department-detail-item">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>位置: 上海市浦东新区</span>
                            </div>
                        </div>
                        <div class="department-actions">
                            <div class="department-action-btn">
                                <i class="fas fa-edit"></i>
                                <span>编辑</span>
                            </div>
                            <div class="department-action-btn">
                                <i class="fas fa-user-plus"></i>
                                <span>添加成员</span>
                            </div>
                        </div>
                    </div>
                    <div class="department-status inactive">筹备中</div>
                </div>
            </div>
        </div>

        <!-- 角色管理标签内容 -->
        <div id="roles-tab" class="tab-content">
            <!-- Filter Tabs -->
            <div class="filter-tabs">
                <div class="filter-tab active">全部</div>
                <div class="filter-tab">系统角色</div>
                <div class="filter-tab">业务角色</div>
                <div class="filter-tab">自定义角色</div>
            </div>

            <!-- Role List -->
            <div class="card">
                <!-- 系统管理员 -->
                <div class="role-item">
                    <div class="role-icon admin">
                        <i class="fas fa-user-shield"></i>
                    </div>
                    <div class="role-info">
                        <div class="role-name">系统管理员</div>
                        <div class="role-description">拥有系统所有功能的管理权限</div>
                        <div class="role-permissions">
                            <div class="permission-badge">用户管理</div>
                            <div class="permission-badge">角色管理</div>
                            <div class="permission-badge">部门管理</div>
                            <div class="permission-badge">系统配置</div>
                            <div class="permission-badge">数据管理</div>
                        </div>
                    </div>
                    <div class="role-count">1人</div>
                </div>

                <!-- 运维主管 -->
                <div class="role-item">
                    <div class="role-icon manager">
                        <i class="fas fa-user-cog"></i>
                    </div>
                    <div class="role-info">
                        <div class="role-name">运维主管</div>
                        <div class="role-description">负责系统运维和设备管理</div>
                        <div class="role-permissions">
                            <div class="permission-badge">设备管理</div>
                            <div class="permission-badge">监控管理</div>
                            <div class="permission-badge">告警管理</div>
                            <div class="permission-badge">日志查看</div>
                        </div>
                    </div>
                    <div class="role-count">2人</div>
                </div>

                <!-- 设备管理员 -->
                <div class="role-item">
                    <div class="role-icon operator">
                        <i class="fas fa-tools"></i>
                    </div>
                    <div class="role-info">
                        <div class="role-name">设备管理员</div>
                        <div class="role-description">负责设备的日常维护和管理</div>
                        <div class="role-permissions">
                            <div class="permission-badge">设备查看</div>
                            <div class="permission-badge">设备配置</div>
                            <div class="permission-badge">设备维护</div>
                        </div>
                    </div>
                    <div class="role-count">3人</div>
                </div>

                <!-- 运维工程师 -->
                <div class="role-item">
                    <div class="role-icon operator">
                        <i class="fas fa-wrench"></i>
                    </div>
                    <div class="role-info">
                        <div class="role-name">运维工程师</div>
                        <div class="role-description">负责系统的日常运维工作</div>
                        <div class="role-permissions">
                            <div class="permission-badge">监控查看</div>
                            <div class="permission-badge">告警处理</div>
                            <div class="permission-badge">日志查看</div>
                        </div>
                    </div>
                    <div class="role-count">4人</div>
                </div>

                <!-- 数据分析师 -->
                <div class="role-item">
                    <div class="role-icon user">
                        <i class="fas fa-chart-pie"></i>
                    </div>
                    <div class="role-info">
                        <div class="role-name">数据分析师</div>
                        <div class="role-description">负责系统数据的分析和报表生成</div>
                        <div class="role-permissions">
                            <div class="permission-badge">数据查看</div>
                            <div class="permission-badge">报表生成</div>
                            <div class="permission-badge">数据导出</div>
                        </div>
                    </div>
                    <div class="role-count">2人</div>
                </div>

                <!-- 项目经理 -->
                <div class="role-item">
                    <div class="role-icon manager">
                        <i class="fas fa-tasks"></i>
                    </div>
                    <div class="role-info">
                        <div class="role-name">项目经理</div>
                        <div class="role-description">负责项目的整体管理和协调</div>
                        <div class="role-permissions">
                            <div class="permission-badge">项目管理</div>
                            <div class="permission-badge">任务分配</div>
                            <div class="permission-badge">进度查看</div>
                            <div class="permission-badge">报表查看</div>
                        </div>
                    </div>
                    <div class="role-count">3人</div>
                </div>

                <!-- 普通用户 -->
                <div class="role-item">
                    <div class="role-icon user">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="role-info">
                        <div class="role-name">普通用户</div>
                        <div class="role-description">基本的系统访问权限</div>
                        <div class="role-permissions">
                            <div class="permission-badge">基础功能</div>
                            <div class="permission-badge">个人信息</div>
                        </div>
                    </div>
                    <div class="role-count">10人</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function switchTab(tabName) {
            // 隐藏所有标签内容
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            // 取消所有标签的选中状态
            document.querySelectorAll('.segment-item').forEach(item => {
                item.classList.remove('active');
            });

            // 显示选中的标签内容
            document.getElementById(tabName + '-tab').classList.add('active');

            // 设置选中标签的样式
            document.querySelector('.segment-item[onclick="switchTab(\'' + tabName + '\')"]').classList.add('active');

            // 更新搜索框的占位符文本
            const searchInput = document.querySelector('.search-bar input');
            if (tabName === 'users') {
                searchInput.placeholder = '搜索用户...';
            } else if (tabName === 'departments') {
                searchInput.placeholder = '搜索部门...';
            } else if (tabName === 'roles') {
                searchInput.placeholder = '搜索角色...';
            }

            // 如果切换到部门管理标签，初始化部门展开/折叠功能
            if (tabName === 'departments') {
                initDepartmentToggles();
            }
        }

        function initDepartmentToggles() {
            // 为所有部门展开/折叠按钮添加点击事件
            document.querySelectorAll('.department-toggle').forEach(toggle => {
                if (!toggle.hasAttribute('data-initialized')) {
                    toggle.setAttribute('data-initialized', 'true');
                    toggle.addEventListener('click', function(e) {
                        e.stopPropagation(); // 阻止事件冒泡

                        // 获取当前部门项
                        const departmentItem = this.closest('.department-item');

                        // 获取下一个兄弟元素（可能是子部门容器）
                        const nextElement = departmentItem.parentElement.nextElementSibling;

                        // 检查是否有子部门
                        if (nextElement && nextElement.classList.contains('department-children')) {
                            // 切换子部门的显示/隐藏状态
                            if (nextElement.style.display === 'none') {
                                // 展开子部门
                                nextElement.style.display = 'block';
                                this.querySelector('i').classList.remove('fa-chevron-right');
                                this.querySelector('i').classList.add('fa-chevron-down');

                                // 添加展开动画
                                nextElement.style.opacity = '0';
                                nextElement.style.transform = 'translateY(-10px)';
                                setTimeout(() => {
                                    nextElement.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
                                    nextElement.style.opacity = '1';
                                    nextElement.style.transform = 'translateY(0)';
                                }, 10);
                            } else {
                                // 折叠子部门
                                this.querySelector('i').classList.remove('fa-chevron-down');
                                this.querySelector('i').classList.add('fa-chevron-right');

                                // 添加折叠动画
                                nextElement.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
                                nextElement.style.opacity = '0';
                                nextElement.style.transform = 'translateY(-10px)';
                                setTimeout(() => {
                                    nextElement.style.display = 'none';
                                }, 300);
                            }
                        }
                    });
                }
            });

            // 为部门项添加点击事件（点击整个部门项也可以展开/折叠）
            document.querySelectorAll('.department-item').forEach(item => {
                if (!item.hasAttribute('data-initialized')) {
                    item.setAttribute('data-initialized', 'true');
                    item.addEventListener('click', function(e) {
                        // 如果点击的是操作按钮或状态标签，不触发展开/折叠
                        if (e.target.closest('.department-action-btn') ||
                            e.target.closest('.department-status') ||
                            e.target.closest('.department-toggle')) {
                            return;
                        }

                        // 获取当前部门的展开/折叠按钮
                        const toggle = this.querySelector('.department-toggle');
                        if (toggle) {
                            // 模拟点击展开/折叠按钮
                            toggle.click();
                        }
                    });
                }
            });

            // 添加部门项的悬停效果
            document.querySelectorAll('.department-item').forEach(item => {
                item.addEventListener('mouseenter', function() {
                    this.style.backgroundColor = 'rgba(0, 122, 255, 0.03)';
                });

                item.addEventListener('mouseleave', function() {
                    this.style.backgroundColor = '';
                });
            });

            // 添加操作按钮的悬停效果
            document.querySelectorAll('.department-action-btn').forEach(btn => {
                btn.addEventListener('mouseenter', function() {
                    this.style.backgroundColor = 'rgba(0, 122, 255, 0.12)';
                });

                btn.addEventListener('mouseleave', function() {
                    this.style.backgroundColor = 'rgba(0, 122, 255, 0.08)';
                });
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 如果当前是部门管理标签，初始化部门展开/折叠功能
            if (document.querySelector('.segment-item[onclick="switchTab(\'departments\')"]').classList.contains('active')) {
                initDepartmentToggles();
            }

            // 初始化权限组复选框联动
            initPermissionCheckboxes();
        });

        // 显示新增弹窗或跳转到新增页面
        function showAddModal() {
            // 获取当前激活的标签
            const activeTab = document.querySelector('.segment-item.active').textContent.trim();

            // 根据当前标签执行不同的操作
            if (activeTab === '角色管理') {
                // 跳转到新增角色页面
                window.location.href = 'role-add.html';
            } else if (activeTab === '用户管理') {
                alert('请在用户管理标签下实现新增用户功能');
            } else if (activeTab === '部门管理') {
                alert('请在部门管理标签下实现新增部门功能');
            }
        }

        // 关闭新增角色弹窗
        function closeAddModal() {
            document.getElementById('add-role-modal').style.display = 'none';
            // 恢复页面滚动
            document.body.style.overflow = 'auto';
            // 重置表单
            document.getElementById('role-name').value = '';
            document.getElementById('data-permission').value = 'all';
            document.getElementById('role-description').value = '';
            // 重置所有复选框
            document.querySelectorAll('.permission-group-checkbox, .permission-item-checkbox').forEach(checkbox => {
                checkbox.checked = false;
            });
        }

        // 初始化权限复选框联动
        function initPermissionCheckboxes() {
            // 为所有权限组复选框添加事件监听
            document.querySelectorAll('.permission-group-checkbox').forEach(groupCheckbox => {
                groupCheckbox.addEventListener('change', function() {
                    // 获取当前组内的所有子项复选框
                    const groupItems = this.closest('.permission-group').querySelectorAll('.permission-item-checkbox');
                    // 将子项复选框的状态与组复选框同步
                    groupItems.forEach(itemCheckbox => {
                        itemCheckbox.checked = this.checked;
                    });
                });
            });

            // 为所有权限子项复选框添加事件监听
            document.querySelectorAll('.permission-item-checkbox').forEach(itemCheckbox => {
                itemCheckbox.addEventListener('change', function() {
                    // 获取当前子项所属的组
                    const group = this.closest('.permission-group');
                    // 获取组内的所有子项复选框
                    const groupItems = group.querySelectorAll('.permission-item-checkbox');
                    // 获取组复选框
                    const groupCheckbox = group.querySelector('.permission-group-checkbox');

                    // 检查组内是否所有子项都被选中
                    let allChecked = true;
                    groupItems.forEach(item => {
                        if (!item.checked) {
                            allChecked = false;
                        }
                    });

                    // 更新组复选框状态
                    groupCheckbox.checked = allChecked;
                });
            });
        }

        // 保存角色
        function saveRole() {
            // 获取表单数据
            const roleName = document.getElementById('role-name').value.trim();
            const dataPermission = document.getElementById('data-permission').value;
            const roleDescription = document.getElementById('role-description').value.trim();

            // 表单验证
            if (!roleName) {
                alert('请输入角色名称');
                return;
            }

            // 获取选中的权限
            const selectedPermissions = [];
            document.querySelectorAll('.permission-item-checkbox:checked').forEach(checkbox => {
                selectedPermissions.push(checkbox.id);
            });

            // 构建角色数据
            const roleData = {
                name: roleName,
                dataPermission: dataPermission,
                description: roleDescription,
                permissions: selectedPermissions
            };

            // 这里应该是将数据发送到服务器的代码
            // 由于这是前端演示，我们只打印数据并模拟添加成功
            console.log('新增角色数据:', roleData);

            // 模拟添加成功
            alert('角色添加成功');

            // 关闭弹窗
            closeAddModal();

            // 添加新角色到列表（实际应用中应该是从服务器获取最新数据）
            addRoleToList(roleData);
        }

        // 添加角色到列表
        function addRoleToList(roleData) {
            // 获取角色列表容器
            const roleList = document.querySelector('#roles-tab .card');

            // 创建新角色元素
            const roleItem = document.createElement('div');
            roleItem.className = 'role-item';

            // 根据数据权限设置图标样式
            let iconClass = 'user';
            if (roleData.dataPermission === 'all') {
                iconClass = 'admin';
            } else if (roleData.dataPermission === 'department-and-children') {
                iconClass = 'manager';
            } else if (roleData.dataPermission === 'department') {
                iconClass = 'operator';
            }

            // 构建权限标签HTML
            let permissionBadges = '';
            roleData.permissions.forEach(permission => {
                // 将ID转换为显示名称
                let permissionName = permission.replace(/-/g, ' ');
                // 首字母大写
                permissionName = permissionName.split(' ').map(word =>
                    word.charAt(0).toUpperCase() + word.slice(1)
                ).join(' ');
                permissionBadges += `<div class="permission-badge">${permissionName}</div>`;
            });

            // 设置角色项的HTML
            roleItem.innerHTML = `
                <div class="role-icon ${iconClass}">
                    <i class="fas fa-user-cog"></i>
                </div>
                <div class="role-info">
                    <div class="role-name">${roleData.name}</div>
                    <div class="role-description">${roleData.description}</div>
                    <div class="role-permissions">
                        ${permissionBadges}
                    </div>
                </div>
                <div class="role-count">新建</div>
            `;

            // 添加到列表开头
            roleList.insertBefore(roleItem, roleList.firstChild);

            // 添加动画效果
            roleItem.style.opacity = '0';
            roleItem.style.transform = 'translateY(-20px)';
            setTimeout(() => {
                roleItem.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
                roleItem.style.opacity = '1';
                roleItem.style.transform = 'translateY(0)';
            }, 10);
        }
    </script>

    <!-- Add Button -->
    <div class="add-button animate-pulse" onclick="showAddModal()">
        <i class="fas fa-plus"></i>
    </div>

    <!-- Navigation Bar -->
    <div class="nav-bar">
        <a href="home.html" class="nav-item">
            <i class="nav-icon fas fa-home"></i>
            <span>首页</span>
        </a>
        <a href="gateway.html" class="nav-item">
            <i class="nav-icon fas fa-th-large"></i>
            <span>功能</span>
        </a>
        <a href="users.html" class="nav-item active">
            <i class="nav-icon fas fa-cog"></i>
            <span>系统</span>
        </a>
        <a href="profile.html" class="nav-item">
            <i class="nav-icon fas fa-user"></i>
            <span>我的</span>
        </a>
    </div>
</body>
</html>
