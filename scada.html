<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>组态管理 - 集成商登陆平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        .search-bar {
            display: flex;
            align-items: center;
            background-color: var(--light-gray);
            border-radius: 10px;
            padding: 8px 12px;
            margin-bottom: 16px;
        }

        .search-bar input {
            flex: 1;
            border: none;
            background: transparent;
            margin-left: 8px;
            font-size: 15px;
        }

        .search-bar input:focus {
            outline: none;
        }

        .scada-card {
            background-color: white;
            border-radius: 12px;
            margin-bottom: 16px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            overflow: hidden;
        }

        .scada-preview {
            height: 180px;
            background-size: cover;
            background-position: center;
            position: relative;
        }

        .scada-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 16px;
            background: linear-gradient(to top, rgba(0,0,0,0.7), transparent);
            color: white;
        }

        .scada-name {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .scada-type {
            font-size: 14px;
            opacity: 0.8;
        }

        .scada-content {
            padding: 16px;
        }

        .scada-description {
            font-size: 15px;
            color: var(--dark-gray);
            margin-bottom: 12px;
        }

        .scada-info {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 12px;
        }

        .scada-info-item {
            display: flex;
            align-items: center;
            font-size: 13px;
            color: var(--dark-gray);
        }

        .scada-info-item i {
            margin-right: 4px;
            font-size: 12px;
        }

        .scada-actions {
            display: flex;
            gap: 8px;
        }

        .scada-action-btn {
            flex: 1;
            padding: 8px 0;
            border-radius: 8px;
            font-size: 13px;
            text-align: center;
            background-color: var(--light-gray);
            color: var(--dark-gray);
        }

        .scada-action-btn.primary {
            background-color: rgba(0, 122, 255, 0.1);
            color: var(--primary-color);
        }

        .component-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }

        .component-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }

        .component-icon {
            width: 50px;
            height: 50px;
            background-color: rgba(0, 122, 255, 0.1);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-color);
            font-size: 24px;
            margin-bottom: 8px;
        }

        .component-name {
            font-size: 12px;
            color: var(--text-color);
        }

        .section-title {
            font-size: 17px;
            font-weight: 600;
            margin-bottom: 12px;
        }
    </style>
</head>
<body>
    <!-- Status Bar -->
    <div class="status-bar">
        <div>9:41</div>
        <div class="status-bar-icons">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>

    <!-- Header -->
    <div class="header">
        <div class="header-title">组态管理</div>
        <div class="header-right">
            <i class="fas fa-plus text-primary-500"></i>
        </div>
    </div>

    <!-- Content -->
    <div class="content">
        <!-- Search Bar -->
        <div class="search-bar">
            <i class="fas fa-search text-gray-400"></i>
            <input type="text" placeholder="搜索组态...">
        </div>

        <!-- Recent Projects -->
        <div class="section-title">最近项目</div>

        <div class="scada-card">
            <div class="scada-preview" style="background-image: url('https://images.unsplash.com/photo-1581092921461-39b9d08a9b21?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80')">
                <div class="scada-overlay">
                    <div class="scada-name">智慧园区监控大屏</div>
                    <div class="scada-type">监控大屏</div>
                </div>
            </div>
            <div class="scada-content">
                <div class="scada-description">
                    园区综合监控大屏，展示设备状态、能耗数据和安防信息
                </div>
                <div class="scada-info">
                    <div class="scada-info-item">
                        <i class="fas fa-clock"></i>
                        <span>更新于: 今天 08:30</span>
                    </div>
                    <div class="scada-info-item">
                        <i class="fas fa-th-large"></i>
                        <span>组件: 24个</span>
                    </div>
                </div>
                <div class="scada-actions">
                    <div class="scada-action-btn">
                        <i class="fas fa-eye mr-1"></i> 预览
                    </div>
                    <div class="scada-action-btn primary">
                        <i class="fas fa-edit mr-1"></i> 编辑
                    </div>
                </div>
            </div>
        </div>

        <div class="scada-card">
            <div class="scada-preview" style="background-image: url('https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80')">
                <div class="scada-overlay">
                    <div class="scada-name">能源消耗分析大屏</div>
                    <div class="scada-type">数据分析</div>
                </div>
            </div>
            <div class="scada-content">
                <div class="scada-description">
                    园区能源消耗分析大屏，展示各区域能耗数据和节能建议
                </div>
                <div class="scada-info">
                    <div class="scada-info-item">
                        <i class="fas fa-clock"></i>
                        <span>更新于: 昨天 15:20</span>
                    </div>
                    <div class="scada-info-item">
                        <i class="fas fa-th-large"></i>
                        <span>组件: 18个</span>
                    </div>
                </div>
                <div class="scada-actions">
                    <div class="scada-action-btn">
                        <i class="fas fa-eye mr-1"></i> 预览
                    </div>
                    <div class="scada-action-btn primary">
                        <i class="fas fa-edit mr-1"></i> 编辑
                    </div>
                </div>
            </div>
        </div>

        <!-- Component Library -->
        <div class="section-title">组件库</div>

        <div class="card mb-6">
            <div class="card-content">
                <div class="component-grid">
                    <div class="component-item">
                        <div class="component-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="component-name">折线图</div>
                    </div>
                    <div class="component-item">
                        <div class="component-icon">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <div class="component-name">柱状图</div>
                    </div>
                    <div class="component-item">
                        <div class="component-icon">
                            <i class="fas fa-chart-pie"></i>
                        </div>
                        <div class="component-name">饼图</div>
                    </div>
                    <div class="component-item">
                        <div class="component-icon">
                            <i class="fas fa-table"></i>
                        </div>
                        <div class="component-name">表格</div>
                    </div>
                    <div class="component-item">
                        <div class="component-icon">
                            <i class="fas fa-map-marked-alt"></i>
                        </div>
                        <div class="component-name">地图</div>
                    </div>
                    <div class="component-item">
                        <div class="component-icon">
                            <i class="fas fa-tachometer-alt"></i>
                        </div>
                        <div class="component-name">仪表盘</div>
                    </div>
                    <div class="component-item">
                        <div class="component-icon">
                            <i class="fas fa-list-alt"></i>
                        </div>
                        <div class="component-name">列表</div>
                    </div>
                    <div class="component-item">
                        <div class="component-icon">
                            <i class="fas fa-image"></i>
                        </div>
                        <div class="component-name">图片</div>
                    </div>
                </div>

                <div class="text-center">
                    <a href="#" class="text-primary-500 text-sm">查看全部组件</a>
                </div>
            </div>
        </div>

        <!-- Templates -->
        <div class="section-title">模板库</div>

        <div class="card">
            <div>
                <div class="list-item">
                    <div>
                        <div class="list-item-title">设备监控模板</div>
                        <div class="list-item-subtitle">适用于设备状态监控大屏</div>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
                <div class="list-item">
                    <div>
                        <div class="list-item-title">能源分析模板</div>
                        <div class="list-item-subtitle">适用于能源消耗分析大屏</div>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
                <div class="list-item">
                    <div>
                        <div class="list-item-title">安防监控模板</div>
                        <div class="list-item-subtitle">适用于安防监控大屏</div>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
                <div class="list-item">
                    <div>
                        <div class="list-item-title">环境监测模板</div>
                        <div class="list-item-subtitle">适用于环境数据监测大屏</div>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation Bar -->
    <div class="nav-bar">
        <a href="home.html" class="nav-item">
            <i class="nav-icon fas fa-home"></i>
            <span>首页</span>
        </a>
        <a href="modules.html" class="nav-item active">
            <i class="nav-icon fas fa-th-large"></i>
            <span>功能</span>
        </a>
        <a href="users.html" class="nav-item">
            <i class="nav-icon fas fa-cog"></i>
            <span>系统</span>
        </a>
        <a href="profile.html" class="nav-item">
            <i class="nav-icon fas fa-user"></i>
            <span>我的</span>
        </a>
    </div>
</body>
</html>
