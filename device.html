<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备配置 - 项目管理平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        .search-bar {
            display: flex;
            align-items: center;
            background-color: var(--light-gray);
            border-radius: 10px;
            padding: 8px 12px;
            margin-bottom: 16px;
        }

        .search-bar input {
            flex: 1;
            border: none;
            background: transparent;
            margin-left: 8px;
            font-size: 15px;
        }

        .search-bar input:focus {
            outline: none;
        }

        .segment-control {
            display: flex;
            background-color: var(--light-gray);
            border-radius: 10px;
            padding: 4px;
            margin-bottom: 20px;
        }

        .segment-item {
            flex: 1;
            text-align: center;
            padding: 8px 0;
            font-size: 14px;
            border-radius: 8px;
        }

        .segment-item.active {
            background-color: white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            font-weight: 600;
        }

        .device-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }

        .device-card {
            background-color: white;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .device-icon {
            width: 40px;
            height: 40px;
            background-color: rgba(0, 122, 255, 0.1);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-color);
            font-size: 20px;
            margin-bottom: 12px;
        }

        .device-name {
            font-size: 15px;
            font-weight: 600;
            margin-bottom: 4px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .device-type {
            font-size: 12px;
            color: var(--dark-gray);
            margin-bottom: 8px;
        }

        .device-status {
            display: flex;
            align-items: center;
            font-size: 12px;
        }

        .status-indicator {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            margin-right: 4px;
        }

        .status-online {
            background-color: var(--success-color);
        }

        .status-offline {
            background-color: var(--danger-color);
        }

        .device-group {
            margin-bottom: 24px;
        }

        .group-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .group-title {
            font-size: 17px;
            font-weight: 600;
        }

        .group-count {
            font-size: 14px;
            color: var(--dark-gray);
        }

        /* 浮动添加按钮样式 */
        .add-button {
            position: fixed;
            bottom: 100px;
            right: 20px;
            width: 56px;
            height: 56px;
            border-radius: 50%;
            background-color: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
            z-index: 50;
            transition: all var(--transition-fast);
        }

        .add-button:active {
            transform: scale(0.95);
            box-shadow: 0 2px 8px rgba(0, 122, 255, 0.2);
        }

        /* 设备类型和分组样式 */
        .type-card, .group-card {
            background-color: white;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            transition: all var(--transition-fast);
        }

        .type-card:active, .group-card:active {
            transform: translateY(2px);
            box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
        }

        .type-icon, .group-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            margin-right: 16px;
            flex-shrink: 0;
        }

        .type-info, .group-info {
            flex: 1;
        }

        .type-name, .group-name {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .type-count, .group-count-text {
            font-size: 13px;
            color: var(--dark-gray);
        }

        .type-actions, .group-actions {
            display: flex;
            gap: 8px;
        }

        .action-icon {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background-color: var(--light-gray);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--dark-gray);
            font-size: 14px;
            transition: all var(--transition-fast);
        }

        .action-icon:active {
            transform: scale(0.9);
            background-color: rgba(0, 0, 0, 0.05);
        }

        /* 内容区域样式 */
        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <!-- Status Bar -->
    <div class="status-bar">
        <div>9:41</div>
        <div class="status-bar-icons">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>

    <!-- Header -->
    <div class="header">
        <div class="header-left">
            <a href="modules.html" class="text-gray-600">
                <i class="fas fa-arrow-left"></i>
            </a>
        </div>
        <div class="header-title">设备配置</div>
        <div class="header-right">
            <i class="fas fa-search text-gray-600"></i>
        </div>
    </div>

    <!-- Content -->
    <div class="content">
        <!-- Search Bar -->
        <div class="search-bar">
            <i class="fas fa-search text-gray-400"></i>
            <input type="text" placeholder="搜索设备...">
        </div>

        <!-- Segment Control -->
        <div class="segment-control">
            <div class="segment-item active" onclick="switchTab('devices')">设备管理</div>
            <div class="segment-item" onclick="switchTab('types')">设备类型</div>
            <div class="segment-item" onclick="switchTab('groups')">设备分组</div>
        </div>

        <!-- 设备管理内容 -->
        <div id="devices-tab" class="tab-content active">
            <div class="device-group">
                <div class="group-header">
                    <div class="group-title">照明设备</div>
                    <div class="group-count">8 个设备</div>
                </div>

                <div class="device-grid">
                    <div class="device-card">
                        <div class="device-icon">
                            <i class="fas fa-lightbulb"></i>
                        </div>
                        <div class="device-name">主楼大厅灯</div>
                        <div class="device-type">智能照明</div>
                        <div class="device-status">
                            <div class="status-indicator status-online"></div>
                            <span class="text-green-500">在线</span>
                        </div>
                    </div>

                    <div class="device-card">
                        <div class="device-icon">
                            <i class="fas fa-lightbulb"></i>
                        </div>
                        <div class="device-name">走廊灯带</div>
                        <div class="device-type">智能照明</div>
                        <div class="device-status">
                            <div class="status-indicator status-online"></div>
                            <span class="text-green-500">在线</span>
                        </div>
                    </div>

                    <div class="device-card">
                        <div class="device-icon">
                            <i class="fas fa-lightbulb"></i>
                        </div>
                        <div class="device-name">会议室灯光</div>
                        <div class="device-type">智能照明</div>
                        <div class="device-status">
                            <div class="status-indicator status-offline"></div>
                            <span class="text-red-500">离线</span>
                        </div>
                    </div>

                    <div class="device-card">
                        <div class="device-icon">
                            <i class="fas fa-lightbulb"></i>
                        </div>
                        <div class="device-name">办公区灯光</div>
                        <div class="device-type">智能照明</div>
                        <div class="device-status">
                            <div class="status-indicator status-online"></div>
                            <span class="text-green-500">在线</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="device-group">
                <div class="group-header">
                    <div class="group-title">环境监测</div>
                    <div class="group-count">6 个设备</div>
                </div>

                <div class="device-grid">
                    <div class="device-card">
                        <div class="device-icon">
                            <i class="fas fa-temperature-high"></i>
                        </div>
                        <div class="device-name">温湿度传感器</div>
                        <div class="device-type">环境监测</div>
                        <div class="device-status">
                            <div class="status-indicator status-online"></div>
                            <span class="text-green-500">在线</span>
                        </div>
                    </div>

                    <div class="device-card">
                        <div class="device-icon">
                            <i class="fas fa-wind"></i>
                        </div>
                        <div class="device-name">空气质量检测</div>
                        <div class="device-type">环境监测</div>
                        <div class="device-status">
                            <div class="status-indicator status-online"></div>
                            <span class="text-green-500">在线</span>
                        </div>
                    </div>

                    <div class="device-card">
                        <div class="device-icon">
                            <i class="fas fa-tint"></i>
                        </div>
                        <div class="device-name">水质监测</div>
                        <div class="device-type">环境监测</div>
                        <div class="device-status">
                            <div class="status-indicator status-offline"></div>
                            <span class="text-red-500">离线</span>
                        </div>
                    </div>

                    <div class="device-card">
                        <div class="device-icon">
                            <i class="fas fa-sun"></i>
                        </div>
                        <div class="device-name">光照传感器</div>
                        <div class="device-type">环境监测</div>
                        <div class="device-status">
                            <div class="status-indicator status-online"></div>
                            <span class="text-green-500">在线</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 设备类型内容 -->
        <div id="types-tab" class="tab-content">
            <div class="type-card">
                <div class="type-icon" style="background-color: #FF9500;">
                    <i class="fas fa-lightbulb"></i>
                </div>
                <div class="type-info">
                    <div class="type-name">智能照明</div>
                    <div class="type-count">8 个设备</div>
                </div>
                <div class="type-actions">
                    <div class="action-icon">
                        <i class="fas fa-edit"></i>
                    </div>
                    <div class="action-icon">
                        <i class="fas fa-ellipsis-v"></i>
                    </div>
                </div>
            </div>

            <div class="type-card">
                <div class="type-icon" style="background-color: #34C759;">
                    <i class="fas fa-temperature-high"></i>
                </div>
                <div class="type-info">
                    <div class="type-name">环境监测</div>
                    <div class="type-count">6 个设备</div>
                </div>
                <div class="type-actions">
                    <div class="action-icon">
                        <i class="fas fa-edit"></i>
                    </div>
                    <div class="action-icon">
                        <i class="fas fa-ellipsis-v"></i>
                    </div>
                </div>
            </div>

            <div class="type-card">
                <div class="type-icon" style="background-color: #5856D6;">
                    <i class="fas fa-door-open"></i>
                </div>
                <div class="type-info">
                    <div class="type-name">门禁控制</div>
                    <div class="type-count">4 个设备</div>
                </div>
                <div class="type-actions">
                    <div class="action-icon">
                        <i class="fas fa-edit"></i>
                    </div>
                    <div class="action-icon">
                        <i class="fas fa-ellipsis-v"></i>
                    </div>
                </div>
            </div>

            <div class="type-card">
                <div class="type-icon" style="background-color: #FF2D55;">
                    <i class="fas fa-video"></i>
                </div>
                <div class="type-info">
                    <div class="type-name">视频监控</div>
                    <div class="type-count">12 个设备</div>
                </div>
                <div class="type-actions">
                    <div class="action-icon">
                        <i class="fas fa-edit"></i>
                    </div>
                    <div class="action-icon">
                        <i class="fas fa-ellipsis-v"></i>
                    </div>
                </div>
            </div>

            <div class="type-card">
                <div class="type-icon" style="background-color: #007AFF;">
                    <i class="fas fa-fan"></i>
                </div>
                <div class="type-info">
                    <div class="type-name">空调控制</div>
                    <div class="type-count">6 个设备</div>
                </div>
                <div class="type-actions">
                    <div class="action-icon">
                        <i class="fas fa-edit"></i>
                    </div>
                    <div class="action-icon">
                        <i class="fas fa-ellipsis-v"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- 设备分组内容 -->
        <div id="groups-tab" class="tab-content">
            <div class="group-card">
                <div class="group-icon" style="background-color: #FF9500;">
                    <i class="fas fa-building"></i>
                </div>
                <div class="group-info">
                    <div class="group-name">主楼区域</div>
                    <div class="group-count-text">10 个设备</div>
                </div>
                <div class="group-actions">
                    <div class="action-icon">
                        <i class="fas fa-edit"></i>
                    </div>
                    <div class="action-icon">
                        <i class="fas fa-ellipsis-v"></i>
                    </div>
                </div>
            </div>

            <div class="group-card">
                <div class="group-icon" style="background-color: #34C759;">
                    <i class="fas fa-building"></i>
                </div>
                <div class="group-info">
                    <div class="group-name">东区</div>
                    <div class="group-count-text">8 个设备</div>
                </div>
                <div class="group-actions">
                    <div class="action-icon">
                        <i class="fas fa-edit"></i>
                    </div>
                    <div class="action-icon">
                        <i class="fas fa-ellipsis-v"></i>
                    </div>
                </div>
            </div>

            <div class="group-card">
                <div class="group-icon" style="background-color: #5856D6;">
                    <i class="fas fa-building"></i>
                </div>
                <div class="group-info">
                    <div class="group-name">西区</div>
                    <div class="group-count-text">6 个设备</div>
                </div>
                <div class="group-actions">
                    <div class="action-icon">
                        <i class="fas fa-edit"></i>
                    </div>
                    <div class="action-icon">
                        <i class="fas fa-ellipsis-v"></i>
                    </div>
                </div>
            </div>

            <div class="group-card">
                <div class="group-icon" style="background-color: #FF2D55;">
                    <i class="fas fa-building"></i>
                </div>
                <div class="group-info">
                    <div class="group-name">南区</div>
                    <div class="group-count-text">5 个设备</div>
                </div>
                <div class="group-actions">
                    <div class="action-icon">
                        <i class="fas fa-edit"></i>
                    </div>
                    <div class="action-icon">
                        <i class="fas fa-ellipsis-v"></i>
                    </div>
                </div>
            </div>

            <div class="group-card">
                <div class="group-icon" style="background-color: #007AFF;">
                    <i class="fas fa-building"></i>
                </div>
                <div class="group-info">
                    <div class="group-name">北区</div>
                    <div class="group-count-text">7 个设备</div>
                </div>
                <div class="group-actions">
                    <div class="action-icon">
                        <i class="fas fa-edit"></i>
                    </div>
                    <div class="action-icon">
                        <i class="fas fa-ellipsis-v"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加按钮 -->
    <a href="#" class="add-button animate-pulse">
        <i class="fas fa-plus"></i>
    </a>

    <!-- Navigation Bar -->
    <div class="nav-bar">
        <a href="home.html" class="nav-item">
            <i class="nav-icon fas fa-home"></i>
            <span>首页</span>
        </a>
        <a href="modules.html" class="nav-item active">
            <i class="nav-icon fas fa-th-large"></i>
            <span>功能</span>
        </a>
        <a href="users.html" class="nav-item">
            <i class="nav-icon fas fa-cog"></i>
            <span>系统</span>
        </a>
        <a href="profile.html" class="nav-item">
            <i class="nav-icon fas fa-user"></i>
            <span>我的</span>
        </a>
    </div>

    <script>
        function switchTab(tabName) {
            // 隐藏所有内容
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            // 取消所有标签的选中状态
            document.querySelectorAll('.segment-item').forEach(item => {
                item.classList.remove('active');
            });

            // 显示选中的内容
            document.getElementById(tabName + '-tab').classList.add('active');

            // 设置选中标签的样式
            event.currentTarget.classList.add('active');
        }
    </script>
</body>
</html>
