<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>巡更管理 - 集成商登陆平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        .search-bar {
            display: flex;
            align-items: center;
            background-color: var(--light-gray);
            border-radius: 10px;
            padding: 8px 12px;
            margin-bottom: 16px;
        }

        .search-bar input {
            flex: 1;
            border: none;
            background: transparent;
            margin-left: 8px;
            font-size: 15px;
        }

        .search-bar input:focus {
            outline: none;
        }

        .segment-control {
            display: flex;
            background-color: var(--light-gray);
            border-radius: 10px;
            padding: 4px;
            margin-bottom: 20px;
        }

        .segment-item {
            flex: 1;
            text-align: center;
            padding: 8px 0;
            font-size: 14px;
            border-radius: 8px;
        }

        .segment-item.active {
            background-color: white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            font-weight: 600;
        }

        .patrol-card {
            background-color: white;
            border-radius: 12px;
            margin-bottom: 16px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            overflow: hidden;
        }

        .patrol-header {
            padding: 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid var(--light-gray);
        }

        .patrol-title {
            display: flex;
            align-items: center;
        }

        .patrol-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 18px;
            color: white;
            background: linear-gradient(135deg, #3498db, #2980b9);
        }

        .patrol-name {
            font-size: 17px;
            font-weight: 600;
        }

        .patrol-time {
            font-size: 14px;
            color: var(--dark-gray);
        }

        .patrol-status {
            font-size: 13px;
            padding: 4px 10px;
            border-radius: 100px;
        }

        .patrol-status.pending {
            background-color: rgba(255, 149, 0, 0.1);
            color: var(--warning-color);
        }

        .patrol-status.in-progress {
            background-color: rgba(0, 122, 255, 0.1);
            color: var(--primary-color);
        }

        .patrol-status.completed {
            background-color: rgba(52, 199, 89, 0.1);
            color: var(--success-color);
        }

        .patrol-status.overdue {
            background-color: rgba(255, 59, 48, 0.1);
            color: var(--danger-color);
        }

        .patrol-content {
            padding: 16px;
        }

        .patrol-description {
            font-size: 15px;
            color: var(--dark-gray);
            margin-bottom: 12px;
        }

        .patrol-details {
            background-color: var(--light-gray);
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 16px;
        }

        .patrol-detail-item {
            display: flex;
            margin-bottom: 8px;
        }

        .patrol-detail-item:last-child {
            margin-bottom: 0;
        }

        .detail-label {
            width: 80px;
            font-size: 14px;
            color: var(--dark-gray);
        }

        .detail-value {
            flex: 1;
            font-size: 14px;
        }

        .patrol-actions {
            display: flex;
            gap: 8px;
        }

        .patrol-action-btn {
            flex: 1;
            padding: 8px 0;
            border-radius: 8px;
            font-size: 13px;
            text-align: center;
            background-color: var(--light-gray);
            color: var(--dark-gray);
        }

        .patrol-action-btn.primary {
            background-color: rgba(0, 122, 255, 0.1);
            color: var(--primary-color);
        }

        .filter-tabs {
            display: flex;
            overflow-x: auto;
            margin-bottom: 16px;
            -webkit-overflow-scrolling: touch;
        }

        .filter-tab {
            padding: 8px 16px;
            border-radius: 100px;
            font-size: 14px;
            white-space: nowrap;
            margin-right: 8px;
        }

        .filter-tab.active {
            background-color: var(--primary-color);
            color: white;
        }

        .filter-tab:not(.active) {
            background-color: var(--light-gray);
            color: var(--dark-gray);
        }

        .patrol-progress {
            height: 6px;
            background-color: var(--light-gray);
            border-radius: 3px;
            margin: 8px 0 12px;
            overflow: hidden;
        }

        .progress-bar {
            height: 100%;
            background-color: var(--primary-color);
        }

        .checkpoint-list {
            margin-top: 12px;
        }

        .checkpoint-item {
            display: flex;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid var(--light-gray);
        }

        .checkpoint-item:last-child {
            border-bottom: none;
        }

        .checkpoint-status {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 10px;
            color: white;
        }

        .checkpoint-status.completed {
            background-color: var(--success-color);
        }

        .checkpoint-status.pending {
            background-color: var(--medium-gray);
        }

        .checkpoint-info {
            flex: 1;
        }

        .checkpoint-name {
            font-size: 14px;
            font-weight: 500;
        }

        .checkpoint-location {
            font-size: 12px;
            color: var(--dark-gray);
        }

        .checkpoint-time {
            font-size: 12px;
            color: var(--dark-gray);
        }

        /* 标签内容区域 */
        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* 巡更路线样式 */
        .route-card {
            background-color: white;
            border-radius: 12px;
            margin-bottom: 16px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            overflow: hidden;
        }

        .route-header {
            padding: 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid var(--light-gray);
        }

        .route-title {
            display: flex;
            align-items: center;
        }

        .route-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 18px;
            color: white;
            background: linear-gradient(135deg, #4CAF50, #2E7D32);
        }

        .route-name {
            font-size: 17px;
            font-weight: 600;
        }

        .route-info {
            font-size: 14px;
            color: var(--dark-gray);
        }

        .route-content {
            padding: 16px;
        }

        .route-description {
            font-size: 15px;
            color: var(--dark-gray);
            margin-bottom: 12px;
        }

        .route-map {
            height: 160px;
            background-color: var(--light-gray);
            border-radius: 8px;
            margin-bottom: 16px;
            position: relative;
            overflow: hidden;
        }

        .route-map img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .route-checkpoints {
            margin-bottom: 16px;
        }

        .checkpoint-count {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 8px;
        }

        .checkpoint-badges {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .checkpoint-badge {
            background-color: var(--light-gray);
            border-radius: 16px;
            padding: 4px 10px;
            font-size: 12px;
            color: var(--dark-gray);
        }

        .route-actions {
            display: flex;
            gap: 8px;
        }

        .route-action-btn {
            flex: 1;
            padding: 8px 0;
            border-radius: 8px;
            font-size: 13px;
            text-align: center;
            background-color: var(--light-gray);
            color: var(--dark-gray);
        }

        .route-action-btn.primary {
            background-color: rgba(0, 122, 255, 0.1);
            color: var(--primary-color);
        }

        /* 巡更记录样式 */
        .record-card {
            background-color: white;
            border-radius: 12px;
            margin-bottom: 16px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            overflow: hidden;
        }

        .record-header {
            padding: 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid var(--light-gray);
        }

        .record-title {
            display: flex;
            align-items: center;
        }

        .record-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 18px;
            color: white;
            background: linear-gradient(135deg, #9C27B0, #673AB7);
        }

        .record-name {
            font-size: 17px;
            font-weight: 600;
        }

        .record-time {
            font-size: 14px;
            color: var(--dark-gray);
        }

        .record-status {
            font-size: 13px;
            padding: 4px 10px;
            border-radius: 100px;
        }

        .record-status.normal {
            background-color: rgba(52, 199, 89, 0.1);
            color: var(--success-color);
        }

        .record-status.abnormal {
            background-color: rgba(255, 59, 48, 0.1);
            color: var(--danger-color);
        }

        .record-content {
            padding: 16px;
        }

        .record-info {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            margin-bottom: 16px;
        }

        .record-info-item {
            display: flex;
            align-items: center;
            font-size: 14px;
            color: var(--dark-gray);
        }

        .record-info-item i {
            margin-right: 6px;
            color: var(--primary-color);
        }

        .record-summary {
            background-color: var(--light-gray);
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 16px;
        }

        .summary-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .summary-item:last-child {
            margin-bottom: 0;
        }

        .summary-label {
            color: var(--dark-gray);
        }

        .summary-value {
            font-weight: 500;
        }

        .summary-value.success {
            color: var(--success-color);
        }

        .summary-value.warning {
            color: var(--warning-color);
        }

        .summary-value.danger {
            color: var(--danger-color);
        }

        .record-actions {
            display: flex;
            gap: 8px;
        }

        .record-action-btn {
            flex: 1;
            padding: 8px 0;
            border-radius: 8px;
            font-size: 13px;
            text-align: center;
            background-color: var(--light-gray);
            color: var(--dark-gray);
        }

        .record-action-btn.primary {
            background-color: rgba(0, 122, 255, 0.1);
            color: var(--primary-color);
        }

        /* 浮动添加按钮样式 */
        .add-button {
            position: fixed;
            bottom: 100px;
            right: 20px;
            width: 56px;
            height: 56px;
            border-radius: 50%;
            background-color: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
            z-index: 50;
            transition: all var(--transition-fast);
        }

        .add-button:active {
            transform: scale(0.95);
            box-shadow: 0 2px 8px rgba(0, 122, 255, 0.2);
        }
    </style>
</head>
<body>
    <!-- Status Bar -->
    <div class="status-bar">
        <div>9:41</div>
        <div class="status-bar-icons">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>

    <!-- Header -->
    <div class="header">
        <div class="header-title">巡更管理</div>
        <div class="header-right">
            <i class="fas fa-plus text-primary-500"></i>
        </div>
    </div>

    <!-- Content -->
    <div class="content">
        <!-- Search Bar -->
        <div class="search-bar">
            <i class="fas fa-search text-gray-400"></i>
            <input type="text" placeholder="搜索巡更任务...">
        </div>

        <!-- Segment Control -->
        <div class="segment-control">
            <div class="segment-item active" onclick="switchTab('tasks')">巡更任务</div>
            <div class="segment-item" onclick="switchTab('routes')">巡更路线</div>
            <div class="segment-item" onclick="switchTab('records')">巡更记录</div>
        </div>

        <!-- 巡更任务标签内容 -->
        <div id="tasks-tab" class="tab-content active">
            <!-- Filter Tabs -->
            <div class="filter-tabs">
                <div class="filter-tab active">全部</div>
                <div class="filter-tab">待执行</div>
                <div class="filter-tab">进行中</div>
                <div class="filter-tab">已完成</div>
                <div class="filter-tab">已逾期</div>
            </div>

            <!-- Patrol Tasks -->
            <div class="patrol-card">
            <div class="patrol-header">
                <div class="patrol-title">
                    <div class="patrol-icon">
                        <i class="fas fa-route"></i>
                    </div>
                    <div>
                        <div class="patrol-name">A栋日常巡检</div>
                        <div class="patrol-time">今天 14:00-16:00</div>
                    </div>
                </div>
                <div class="patrol-status in-progress">进行中</div>
            </div>
            <div class="patrol-content">
                <div class="patrol-description">
                    A栋办公楼设备日常巡检，包含空调、照明、安防等设备检查
                </div>
                <div class="patrol-details">
                    <div class="patrol-detail-item">
                        <div class="detail-label">巡检人:</div>
                        <div class="detail-value">王工</div>
                    </div>
                    <div class="patrol-detail-item">
                        <div class="detail-label">检查点:</div>
                        <div class="detail-value">8个 (已完成5个)</div>
                    </div>
                </div>

                <div class="patrol-progress">
                    <div class="progress-bar" style="width: 62.5%"></div>
                </div>

                <div class="checkpoint-list">
                    <div class="checkpoint-item">
                        <div class="checkpoint-status completed">
                            <i class="fas fa-check"></i>
                        </div>
                        <div class="checkpoint-info">
                            <div class="checkpoint-name">1楼大厅空调</div>
                            <div class="checkpoint-location">A栋1楼</div>
                        </div>
                        <div class="checkpoint-time">14:15</div>
                    </div>
                    <div class="checkpoint-item">
                        <div class="checkpoint-status completed">
                            <i class="fas fa-check"></i>
                        </div>
                        <div class="checkpoint-info">
                            <div class="checkpoint-name">1楼安防设备</div>
                            <div class="checkpoint-location">A栋1楼</div>
                        </div>
                        <div class="checkpoint-time">14:30</div>
                    </div>
                    <div class="checkpoint-item">
                        <div class="checkpoint-status pending">
                        </div>
                        <div class="checkpoint-info">
                            <div class="checkpoint-name">3楼会议室</div>
                            <div class="checkpoint-location">A栋3楼</div>
                        </div>
                        <div class="checkpoint-time">-</div>
                    </div>
                </div>

                <div class="patrol-actions mt-4">
                    <div class="patrol-action-btn">
                        <i class="fas fa-history mr-1"></i> 查看详情
                    </div>
                    <div class="patrol-action-btn primary">
                        <i class="fas fa-map-marker-alt mr-1"></i> 导航路线
                    </div>
                </div>
            </div>
        </div>

        <div class="patrol-card">
            <div class="patrol-header">
                <div class="patrol-title">
                    <div class="patrol-icon">
                        <i class="fas fa-route"></i>
                    </div>
                    <div>
                        <div class="patrol-name">数据中心巡检</div>
                        <div class="patrol-time">今天 10:00-12:00</div>
                    </div>
                </div>
                <div class="patrol-status completed">已完成</div>
            </div>
            <div class="patrol-content">
                <div class="patrol-description">
                    E栋数据中心设备巡检，包含服务器、网络设备、空调等
                </div>
                <div class="patrol-details">
                    <div class="patrol-detail-item">
                        <div class="detail-label">巡检人:</div>
                        <div class="detail-value">李工</div>
                    </div>
                    <div class="patrol-detail-item">
                        <div class="detail-label">检查点:</div>
                        <div class="detail-value">6个 (已完成6个)</div>
                    </div>
                    <div class="patrol-detail-item">
                        <div class="detail-label">完成时间:</div>
                        <div class="detail-value">11:45</div>
                    </div>
                </div>

                <div class="patrol-actions">
                    <div class="patrol-action-btn">
                        <i class="fas fa-file-alt mr-1"></i> 查看报告
                    </div>
                    <div class="patrol-action-btn primary">
                        <i class="fas fa-copy mr-1"></i> 复制任务
                    </div>
                </div>
            </div>
        </div>

        <div class="patrol-card">
            <div class="patrol-header">
                <div class="patrol-title">
                    <div class="patrol-icon">
                        <i class="fas fa-route"></i>
                    </div>
                    <div>
                        <div class="patrol-name">B栋安防巡检</div>
                        <div class="patrol-time">明天 09:00-11:00</div>
                    </div>
                </div>
                <div class="patrol-status pending">待执行</div>
            </div>
            <div class="patrol-content">
                <div class="patrol-description">
                    B栋办公楼安防设备巡检，包含摄像头、门禁、报警器等
                </div>
                <div class="patrol-details">
                    <div class="patrol-detail-item">
                        <div class="detail-label">巡检人:</div>
                        <div class="detail-value">张工</div>
                    </div>
                    <div class="patrol-detail-item">
                        <div class="detail-label">检查点:</div>
                        <div class="detail-value">10个</div>
                    </div>
                </div>

                <div class="patrol-actions">
                    <div class="patrol-action-btn">
                        <i class="fas fa-user-edit mr-1"></i> 更换人员
                    </div>
                    <div class="patrol-action-btn primary">
                        <i class="fas fa-edit mr-1"></i> 编辑任务
                    </div>
                </div>
            </div>
        </div>
        </div>

        <!-- 巡更路线标签内容 -->
        <div id="routes-tab" class="tab-content">
            <!-- Filter Tabs -->
            <div class="filter-tabs">
                <div class="filter-tab active">全部</div>
                <div class="filter-tab">常规路线</div>
                <div class="filter-tab">临时路线</div>
                <div class="filter-tab">已停用</div>
            </div>

            <!-- Route Cards -->
            <div class="route-card">
                <div class="route-header">
                    <div class="route-title">
                        <div class="route-icon">
                            <i class="fas fa-map-marked-alt"></i>
                        </div>
                        <div>
                            <div class="route-name">A栋标准巡检路线</div>
                            <div class="route-info">常规路线 · 8个检查点</div>
                        </div>
                    </div>
                    <div class="patrol-status in-progress">使用中</div>
                </div>
                <div class="route-content">
                    <div class="route-description">
                        A栋办公楼标准巡检路线，覆盖所有重要设备和安全区域
                    </div>
                    <div class="route-map">
                        <img src="https://via.placeholder.com/800x400/e0e0e0/808080?text=A栋平面图" alt="A栋平面图">
                    </div>
                    <div class="route-checkpoints">
                        <div class="checkpoint-count">检查点 (8)</div>
                        <div class="checkpoint-badges">
                            <div class="checkpoint-badge">1楼大厅</div>
                            <div class="checkpoint-badge">1楼安防室</div>
                            <div class="checkpoint-badge">2楼走廊</div>
                            <div class="checkpoint-badge">3楼会议室</div>
                            <div class="checkpoint-badge">3楼办公区</div>
                            <div class="checkpoint-badge">4楼机房</div>
                            <div class="checkpoint-badge">屋顶设备间</div>
                            <div class="checkpoint-badge">消防通道</div>
                        </div>
                    </div>
                    <div class="route-actions">
                        <div class="route-action-btn">
                            <i class="fas fa-history mr-1"></i> 查看记录
                        </div>
                        <div class="route-action-btn primary">
                            <i class="fas fa-edit mr-1"></i> 编辑路线
                        </div>
                    </div>
                </div>
            </div>

            <div class="route-card">
                <div class="route-header">
                    <div class="route-title">
                        <div class="route-icon">
                            <i class="fas fa-map-marked-alt"></i>
                        </div>
                        <div>
                            <div class="route-name">数据中心巡检路线</div>
                            <div class="route-info">常规路线 · 6个检查点</div>
                        </div>
                    </div>
                    <div class="patrol-status in-progress">使用中</div>
                </div>
                <div class="route-content">
                    <div class="route-description">
                        数据中心专用巡检路线，重点检查服务器、网络设备和空调系统
                    </div>
                    <div class="route-map">
                        <img src="https://via.placeholder.com/800x400/e0e0e0/808080?text=数据中心平面图" alt="数据中心平面图">
                    </div>
                    <div class="route-checkpoints">
                        <div class="checkpoint-count">检查点 (6)</div>
                        <div class="checkpoint-badges">
                            <div class="checkpoint-badge">入口安检</div>
                            <div class="checkpoint-badge">主机房</div>
                            <div class="checkpoint-badge">网络机房</div>
                            <div class="checkpoint-badge">UPS室</div>
                            <div class="checkpoint-badge">空调机房</div>
                            <div class="checkpoint-badge">消防设备</div>
                        </div>
                    </div>
                    <div class="route-actions">
                        <div class="route-action-btn">
                            <i class="fas fa-history mr-1"></i> 查看记录
                        </div>
                        <div class="route-action-btn primary">
                            <i class="fas fa-edit mr-1"></i> 编辑路线
                        </div>
                    </div>
                </div>
            </div>

            <div class="route-card">
                <div class="route-header">
                    <div class="route-title">
                        <div class="route-icon">
                            <i class="fas fa-map-marked-alt"></i>
                        </div>
                        <div>
                            <div class="route-name">B栋安防巡检路线</div>
                            <div class="route-info">临时路线 · 10个检查点</div>
                        </div>
                    </div>
                    <div class="patrol-status pending">待启用</div>
                </div>
                <div class="route-content">
                    <div class="route-description">
                        B栋办公楼安防设备临时巡检路线，重点检查摄像头、门禁和报警器
                    </div>
                    <div class="route-map">
                        <img src="https://via.placeholder.com/800x400/e0e0e0/808080?text=B栋平面图" alt="B栋平面图">
                    </div>
                    <div class="route-checkpoints">
                        <div class="checkpoint-count">检查点 (10)</div>
                        <div class="checkpoint-badges">
                            <div class="checkpoint-badge">1楼大厅</div>
                            <div class="checkpoint-badge">1楼安防室</div>
                            <div class="checkpoint-badge">1楼电梯厅</div>
                            <div class="checkpoint-badge">2楼走廊</div>
                            <div class="checkpoint-badge">2楼会议室</div>
                            <div class="checkpoint-badge">3楼走廊</div>
                            <div class="checkpoint-badge">3楼办公区</div>
                            <div class="checkpoint-badge">4楼走廊</div>
                            <div class="checkpoint-badge">4楼办公区</div>
                            <div class="checkpoint-badge">消防通道</div>
                        </div>
                    </div>
                    <div class="route-actions">
                        <div class="route-action-btn">
                            <i class="fas fa-trash-alt mr-1"></i> 删除
                        </div>
                        <div class="route-action-btn primary">
                            <i class="fas fa-check mr-1"></i> 启用路线
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 巡更记录标签内容 -->
        <div id="records-tab" class="tab-content">
            <!-- Filter Tabs -->
            <div class="filter-tabs">
                <div class="filter-tab active">全部</div>
                <div class="filter-tab">正常</div>
                <div class="filter-tab">异常</div>
                <div class="filter-tab">今日</div>
                <div class="filter-tab">本周</div>
            </div>

            <!-- Record Cards -->
            <div class="record-card">
                <div class="record-header">
                    <div class="record-title">
                        <div class="record-icon">
                            <i class="fas fa-clipboard-check"></i>
                        </div>
                        <div>
                            <div class="record-name">A栋日常巡检记录</div>
                            <div class="record-time">今天 14:00-16:00</div>
                        </div>
                    </div>
                    <div class="record-status normal">正常</div>
                </div>
                <div class="record-content">
                    <div class="record-info">
                        <div class="record-info-item">
                            <i class="fas fa-user"></i>
                            <span>巡检人: 王工</span>
                        </div>
                        <div class="record-info-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>路线: A栋标准巡检路线</span>
                        </div>
                        <div class="record-info-item">
                            <i class="fas fa-clock"></i>
                            <span>耗时: 1小时45分钟</span>
                        </div>
                    </div>

                    <div class="record-summary">
                        <div class="summary-item">
                            <div class="summary-label">计划检查点</div>
                            <div class="summary-value">8个</div>
                        </div>
                        <div class="summary-item">
                            <div class="summary-label">实际检查点</div>
                            <div class="summary-value">8个</div>
                        </div>
                        <div class="summary-item">
                            <div class="summary-label">正常点位</div>
                            <div class="summary-value success">8个</div>
                        </div>
                        <div class="summary-item">
                            <div class="summary-label">异常点位</div>
                            <div class="summary-value">0个</div>
                        </div>
                        <div class="summary-item">
                            <div class="summary-label">完成率</div>
                            <div class="summary-value success">100%</div>
                        </div>
                    </div>

                    <div class="record-actions">
                        <div class="record-action-btn">
                            <i class="fas fa-file-alt mr-1"></i> 查看报告
                        </div>
                        <div class="record-action-btn primary">
                            <i class="fas fa-share-alt mr-1"></i> 分享
                        </div>
                    </div>
                </div>
            </div>

            <div class="record-card">
                <div class="record-header">
                    <div class="record-title">
                        <div class="record-icon">
                            <i class="fas fa-clipboard-check"></i>
                        </div>
                        <div>
                            <div class="record-name">数据中心巡检记录</div>
                            <div class="record-time">今天 10:00-12:00</div>
                        </div>
                    </div>
                    <div class="record-status normal">正常</div>
                </div>
                <div class="record-content">
                    <div class="record-info">
                        <div class="record-info-item">
                            <i class="fas fa-user"></i>
                            <span>巡检人: 李工</span>
                        </div>
                        <div class="record-info-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>路线: 数据中心巡检路线</span>
                        </div>
                        <div class="record-info-item">
                            <i class="fas fa-clock"></i>
                            <span>耗时: 1小时45分钟</span>
                        </div>
                    </div>

                    <div class="record-summary">
                        <div class="summary-item">
                            <div class="summary-label">计划检查点</div>
                            <div class="summary-value">6个</div>
                        </div>
                        <div class="summary-item">
                            <div class="summary-label">实际检查点</div>
                            <div class="summary-value">6个</div>
                        </div>
                        <div class="summary-item">
                            <div class="summary-label">正常点位</div>
                            <div class="summary-value success">6个</div>
                        </div>
                        <div class="summary-item">
                            <div class="summary-label">异常点位</div>
                            <div class="summary-value">0个</div>
                        </div>
                        <div class="summary-item">
                            <div class="summary-label">完成率</div>
                            <div class="summary-value success">100%</div>
                        </div>
                    </div>

                    <div class="record-actions">
                        <div class="record-action-btn">
                            <i class="fas fa-file-alt mr-1"></i> 查看报告
                        </div>
                        <div class="record-action-btn primary">
                            <i class="fas fa-share-alt mr-1"></i> 分享
                        </div>
                    </div>
                </div>
            </div>

            <div class="record-card">
                <div class="record-header">
                    <div class="record-title">
                        <div class="record-icon">
                            <i class="fas fa-clipboard-check"></i>
                        </div>
                        <div>
                            <div class="record-name">C栋安防巡检记录</div>
                            <div class="record-time">昨天 15:30-17:00</div>
                        </div>
                    </div>
                    <div class="record-status abnormal">异常</div>
                </div>
                <div class="record-content">
                    <div class="record-info">
                        <div class="record-info-item">
                            <i class="fas fa-user"></i>
                            <span>巡检人: 赵工</span>
                        </div>
                        <div class="record-info-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>路线: C栋安防巡检路线</span>
                        </div>
                        <div class="record-info-item">
                            <i class="fas fa-clock"></i>
                            <span>耗时: 1小时30分钟</span>
                        </div>
                    </div>

                    <div class="record-summary">
                        <div class="summary-item">
                            <div class="summary-label">计划检查点</div>
                            <div class="summary-value">9个</div>
                        </div>
                        <div class="summary-item">
                            <div class="summary-label">实际检查点</div>
                            <div class="summary-value">9个</div>
                        </div>
                        <div class="summary-item">
                            <div class="summary-label">正常点位</div>
                            <div class="summary-value success">7个</div>
                        </div>
                        <div class="summary-item">
                            <div class="summary-label">异常点位</div>
                            <div class="summary-value danger">2个</div>
                        </div>
                        <div class="summary-item">
                            <div class="summary-label">完成率</div>
                            <div class="summary-value success">100%</div>
                        </div>
                    </div>

                    <div class="record-actions">
                        <div class="record-action-btn">
                            <i class="fas fa-exclamation-triangle mr-1"></i> 查看异常
                        </div>
                        <div class="record-action-btn primary">
                            <i class="fas fa-file-alt mr-1"></i> 查看报告
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 浮动添加按钮 -->
    <a href="#" class="add-button animate-pulse">
        <i class="fas fa-plus"></i>
    </a>

    <!-- Navigation Bar -->
    <div class="nav-bar">
        <a href="home.html" class="nav-item">
            <i class="nav-icon fas fa-home"></i>
            <span>首页</span>
        </a>
        <a href="modules.html" class="nav-item active">
            <i class="nav-icon fas fa-th-large"></i>
            <span>功能</span>
        </a>
        <a href="users.html" class="nav-item">
            <i class="nav-icon fas fa-cog"></i>
            <span>系统</span>
        </a>
        <a href="profile.html" class="nav-item">
            <i class="nav-icon fas fa-user"></i>
            <span>我的</span>
        </a>
    </div>

    <script>
        function switchTab(tabName) {
            // 隐藏所有标签内容
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            // 取消所有标签的选中状态
            document.querySelectorAll('.segment-item').forEach(item => {
                item.classList.remove('active');
            });

            // 显示选中的标签内容
            document.getElementById(tabName + '-tab').classList.add('active');

            // 设置选中标签的样式
            document.querySelector('.segment-item[onclick="switchTab(\'' + tabName + '\')"]').classList.add('active');

            // 更新搜索框的占位符文本
            const searchInput = document.querySelector('.search-bar input');
            if (tabName === 'tasks') {
                searchInput.placeholder = '搜索巡更任务...';
            } else if (tabName === 'routes') {
                searchInput.placeholder = '搜索巡更路线...';
            } else if (tabName === 'records') {
                searchInput.placeholder = '搜索巡更记录...';
            }
        }
    </script>
</body>
</html>
